/*
* Copyright © 2025. Toyota Motors North America Inc
* All rights reserved.
*/

package com.toyota.oneapp.features.dashboard.connectedvehicle.dk.mm21.domain.usecase

import com.toyota.oneapp.digitalkey.DigitalkeyCalibration
import com.toyota.oneapp.features.dashboard.connectedvehicle.dk.mm21.domain.model.CalibrationResult
import com.toyota.oneapp.features.dashboard.connectedvehicle.dk.mm21.domain.model.CalibrationStatus
import com.toyota.oneapp.features.dashboard.connectedvehicle.dk.mm21.domain.repository.DigitalKeyGen1Repository
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import javax.inject.Inject

/**
 * Use case for updating digital key calibration settings
 */
class UpdateCalibrationUseCase @Inject constructor(
    private val repository: DigitalKeyGen1Repository
) {
    
    /**
     * Updates the BLE calibration for the current digital key
     * @param calibration The calibration settings to apply
     * @return Flow of CalibrationResult
     */
    operator fun invoke(calibration: DigitalkeyCalibration): Flow<CalibrationResult> = flow {
        try {
            val keyInfo = repository.getVinKeyInfo()
            
            if (keyInfo != null) {
                val currentCalibrationStatus = repository.getCalibrationStatus(keyInfo.keyInfoId)
                
                if (currentCalibrationStatus.isManualSetting) {
                    val result = repository.setBleCalibration(keyInfo, calibration.freqValue)
                    
                    if (result.isSuccess) {
                        // Update local calibration status
                        val updatedStatus = CalibrationStatus(
                            isManualSetting = true,
                            digitalkeyCalibration = calibration,
                            keyInfoId = keyInfo.keyInfoId
                        )
                        repository.updateCalibrationStatus(keyInfo.keyInfoId, updatedStatus)
                        
                        // Reconnect Bluetooth to apply settings
                        repository.reconnectBluetooth()
                        
                        emit(CalibrationResult.Success)
                    } else {
                        emit(CalibrationResult.Failed("Failed to set calibration"))
                    }
                } else {
                    emit(CalibrationResult.Failed("Manual calibration is not enabled"))
                }
            } else {
                emit(CalibrationResult.Failed("Key information not found"))
            }
        } catch (e: Exception) {
            emit(CalibrationResult.Failed(e.message ?: "Unknown calibration error"))
        }
    }
    
    /**
     * Gets the current calibration status
     * @return Flow of CalibrationStatus
     */
    fun getCurrentCalibrationStatus(): Flow<CalibrationStatus> = flow {
        try {
            val keyInfo = repository.getVinKeyInfo()
            
            if (keyInfo != null) {
                val status = repository.getCalibrationStatus(keyInfo.keyInfoId)
                emit(status)
            } else {
                emit(CalibrationStatus.default())
            }
        } catch (e: Exception) {
            emit(CalibrationStatus.default())
        }
    }
    
    /**
     * Enables or disables manual calibration setting
     * @param enabled Whether manual calibration should be enabled
     * @return Flow of CalibrationResult
     */
    fun setManualCalibrationEnabled(enabled: Boolean): Flow<CalibrationResult> = flow {
        try {
            val keyInfo = repository.getVinKeyInfo()
            
            if (keyInfo != null) {
                val currentStatus = repository.getCalibrationStatus(keyInfo.keyInfoId)
                val updatedStatus = currentStatus.copy(isManualSetting = enabled)
                
                repository.updateCalibrationStatus(keyInfo.keyInfoId, updatedStatus)
                emit(CalibrationResult.Success)
            } else {
                emit(CalibrationResult.Failed("Key information not found"))
            }
        } catch (e: Exception) {
            emit(CalibrationResult.Failed(e.message ?: "Failed to update manual setting"))
        }
    }
}
