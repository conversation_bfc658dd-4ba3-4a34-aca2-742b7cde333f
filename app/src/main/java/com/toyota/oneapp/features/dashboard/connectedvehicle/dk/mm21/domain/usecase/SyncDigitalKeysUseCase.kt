/*
* Copyright © 2025. Toyota Motors North America Inc
* All rights reserved.
*/

package com.toyota.oneapp.features.dashboard.connectedvehicle.dk.mm21.domain.usecase

import com.toyota.oneapp.features.dashboard.connectedvehicle.dk.mm21.domain.model.SyncStatus
import com.toyota.oneapp.features.dashboard.connectedvehicle.dk.mm21.domain.repository.DigitalKeyGen1Repository
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import javax.inject.Inject

/**
 * Use case for synchronizing digital keys with the server
 */
class SyncDigitalKeysUseCase @Inject constructor(
    private val repository: DigitalKeyGen1Repository
) {
    
    /**
     * Synchronizes all digital keys with the server
     * @return Flow of SyncStatus representing the synchronization status
     */
    operator fun invoke(): Flow<SyncStatus> = flow {
        emit(SyncStatus.InProgress)
        
        try {
            val result = repository.syncAllKeys()
            
            if (result.isSuccess) {
                emit(SyncStatus.Completed)
            } else {
                emit(SyncStatus.Failed(result.exceptionOrNull()?.message ?: "Sync failed"))
            }
        } catch (e: Exception) {
            emit(SyncStatus.Failed(e.message ?: "Unknown sync error"))
        }
    }
    
    /**
     * Synchronizes a specific key by keyInfoId
     * @param keyInfoId The ID of the key to sync
     * @return Flow of SyncStatus representing the synchronization status
     */
    fun syncSpecificKey(keyInfoId: String): Flow<SyncStatus> = flow {
        emit(SyncStatus.InProgress)
        
        try {
            val result = repository.syncKey(keyInfoId)
            
            if (result.isSuccess) {
                emit(SyncStatus.Completed)
            } else {
                emit(SyncStatus.Failed(result.exceptionOrNull()?.message ?: "Key sync failed"))
            }
        } catch (e: Exception) {
            emit(SyncStatus.Failed(e.message ?: "Unknown key sync error"))
        }
    }
}
