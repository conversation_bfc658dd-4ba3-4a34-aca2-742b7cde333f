/*
* Copyright © 2025. Toyota Motors North America Inc
* All rights reserved.
*/

package com.toyota.oneapp.features.dashboard.connectedvehicle.dk.mm21.domain.model

/**
 * Represents a digital key deletion request
 */
data class KeyDeletionRequest(
    val vin: String,
    val keyInfoId: String?,
    val keyType: KeyRevocationType,
    val inviteId: String? = null,
    val isSelfPersona: Boolean = false,
    val isSharedVehicle: Boolean = false
)

/**
 * Represents a shared key revocation request
 */
data class SharedKeyRevocationRequest(
    val vin: String,
    val keyInfoId: String,
    val inviteId: String,
    val phoneNumber: String,
    val keyType: String
)

/**
 * Represents the current state of digital key operations
 */
data class DigitalKeyOperationState(
    val isLoading: Boolean = false,
    val lastSyncTime: Long? = null,
    val syncStatus: SyncStatus = SyncStatus.Idle,
    val pendingOperations: List<String> = emptyList(),
    val error: String? = null
)

/**
 * Represents device information for digital key operations
 */
data class DeviceInfo(
    val deviceId: String,
    val isBluetoothEnabled: Boolean,
    val isLocationEnabled: Boolean
)
