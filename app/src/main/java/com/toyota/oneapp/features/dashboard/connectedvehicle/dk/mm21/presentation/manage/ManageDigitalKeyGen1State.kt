/*
* Copyright © 2025. Toyota Motors North America Inc
* All rights reserved.
*/

package com.toyota.oneapp.features.dashboard.connectedvehicle.dk.mm21.presentation.manage

import com.toyota.oneapp.features.dashboard.connectedvehicle.dk.domain.model.LuksStatusInvite
import com.toyota.oneapp.features.dashboard.connectedvehicle.dk.mm21.domain.model.SharedDigitalKey
import com.toyota.oneapp.model.vehicle.VehicleInfo

sealed class ManageDigitalKeyGen1State {
    object Loading : ManageDigitalKeyGen1State()

    data class Success(
        val vehicle: VehicleInfo? = null,
        val ownerName: String = "",
        val sharedKeys: List<LuksStatusInvite>? = null,
        val isEligibleForSharing: Boolean = false,
        val canInviteUsers: Boolean = false,
        val maxSharedKeys: Int = SharedDigitalKey.MAX_SHARED_KEYS,
        val isDeleting: Boolean = false,
        val isSyncing: Boolean = false,
        val lastSyncTime: Long? = null,
    ) : ManageDigitalKeyGen1State()

    data class Error(
        val message: String?,
        val errorType: ErrorType = ErrorType.GENERAL,
        val canRetry: Boolean = true,
    ) : ManageDigitalKeyGen1State()
}

/**
 * Types of errors that can occur in the manage screen
 */
enum class ErrorType {
    GENERAL,
    NETWORK,
    AUTHENTICATION,
    KEY_DELETION_FAILED,
    SYNC_FAILED,
    PERMISSION_DENIED
}

data class ManageDigitalKeyGen1Data(
    val keyId: String,
    val vehicleName: String,
    val isConnected: Boolean,
)
