/*
* Copyright © 2025. Toyota Motors North America Inc
* All rights reserved.
*/

package com.toyota.oneapp.features.dashboard.connectedvehicle.dk.mm21.presentation.manage

import android.app.Activity
import android.content.Intent
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.toyota.oneapp.analytics.AnalyticsEvent
import com.toyota.oneapp.analytics.AnalyticsEventParam
import com.toyota.oneapp.analytics.AnalyticsLogger
import com.toyota.oneapp.app.DataDogUtils
import com.toyota.oneapp.digitalkey.DigitalMopKeyUtils
import com.toyota.oneapp.digitalkey.onboarding.DkOnBoardingActivity
import com.toyota.oneapp.features.dashboard.connectedvehicle.dk.domain.model.LuksStatusInvite
import com.toyota.oneapp.features.dashboard.connectedvehicle.dk.mm21.domain.model.DigitalKeyStatus
import com.toyota.oneapp.features.dashboard.connectedvehicle.dk.mm21.domain.model.KeyDeletionRequest
import com.toyota.oneapp.features.dashboard.connectedvehicle.dk.mm21.domain.model.KeyRevocationType
import com.toyota.oneapp.features.dashboard.connectedvehicle.dk.mm21.domain.usecase.DeleteDigitalKeyUseCase
import com.toyota.oneapp.features.dashboard.connectedvehicle.dk.mm21.domain.usecase.GetDigitalKeyOwnerUseCase
import com.toyota.oneapp.features.dashboard.connectedvehicle.dk.mm21.domain.usecase.GetSharedKeysUseCase
import com.toyota.oneapp.features.dashboard.connectedvehicle.dk.mm21.domain.usecase.SyncDigitalKeysUseCase
import com.toyota.oneapp.features.dashboard.connectedvehicle.dk.mm21.domain.model.SyncStatus
import com.toyota.oneapp.model.vehicle.VehicleInfo
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class ManageDigitalKeyGen1ViewModel
    @Inject
    constructor(
        private val getDigitalKeyOwnerUseCase: GetDigitalKeyOwnerUseCase,
        private val getSharedKeysUseCase: GetSharedKeysUseCase,
        private val deleteDigitalKeyUseCase: DeleteDigitalKeyUseCase,
        private val syncDigitalKeysUseCase: SyncDigitalKeysUseCase,
        private val analyticsLogger: AnalyticsLogger,
        private val dataDogUtils: DataDogUtils,
    ) : ViewModel() {
        private val _state = MutableStateFlow<ManageDigitalKeyGen1State>(ManageDigitalKeyGen1State.Loading)
        val state: StateFlow<ManageDigitalKeyGen1State> = _state

        init {
            getOwnerInfo()
        }

        private fun getOwnerInfo() {
            viewModelScope.launch {
                try {
                    _state.value = ManageDigitalKeyGen1State.Loading

                    // Log screen view
                    analyticsLogger.logEventWithParameter(
                        AnalyticsEvent.EVENT_GROUP_DK.eventName,
                        AnalyticsEventParam.DK_MANAGE_SCREEN_VIEW
                    )

                    val ownerInfo = getDigitalKeyOwnerUseCase()
                    val sharedKeys = getSharedKeysUseCase()

                    // Convert SharedDigitalKey to LuksStatusInvite for backward compatibility
                    val luksStatusInvites = sharedKeys.map { it.toLuksStatusInvite() }

                    _state.value = ManageDigitalKeyGen1State.Success(
                        vehicle = null, // Will be populated from application data if needed
                        ownerName = "${ownerInfo.firstName} ${ownerInfo.lastName}".trim(),
                        sharedKeys = luksStatusInvites,
                        isEligibleForSharing = ownerInfo.isOwner,
                        canInviteUsers = ownerInfo.isOwner,
                        lastSyncTime = System.currentTimeMillis()
                    )

                    // Log successful data load
                    DigitalMopKeyUtils.appendLog(
                        "Digital key manage screen loaded successfully. Owner: ${ownerInfo.isOwner}, Shared keys: ${luksStatusInvites.size}",
                        DigitalMopKeyUtils.TAG,
                        isDataDogRequired = true,
                        isError = false
                    )
                } catch (e: Exception) {
                    val errorType = when {
                        e.message?.contains("network", ignoreCase = true) == true -> ErrorType.NETWORK
                        e.message?.contains("permission", ignoreCase = true) == true -> ErrorType.PERMISSION_DENIED
                        e.message?.contains("auth", ignoreCase = true) == true -> ErrorType.AUTHENTICATION
                        else -> ErrorType.GENERAL
                    }

                    // Log error
                    DigitalMopKeyUtils.appendLog(
                        "Failed to load digital key manage screen: ${e.message}",
                        DigitalMopKeyUtils.TAG,
                        isDataDogRequired = true,
                        isError = true
                    )

                    _state.value = ManageDigitalKeyGen1State.Error(
                        message = e.message ?: "Failed to load digital key information",
                        errorType = errorType,
                        canRetry = errorType != ErrorType.PERMISSION_DENIED
                    )
                }
            }
        }

        fun onSetupClick() {
            // Trigger sync of digital keys
            viewModelScope.launch {
                try {
                    val currentState = _state.value
                    if (currentState is ManageDigitalKeyGen1State.Success) {
                        _state.value = currentState.copy(isSyncing = true)

                        syncDigitalKeysUseCase().collect { syncStatus ->
                            when (syncStatus) {
                                is SyncStatus.Completed -> {
                                    // Log successful sync
                                    analyticsLogger.logEventWithParameter(
                                        AnalyticsEvent.EVENT_GROUP_DK.eventName,
                                        AnalyticsEventParam.DK_SYNC_SUCCESS
                                    )

                                    DigitalMopKeyUtils.appendLog(
                                        "Digital key sync completed successfully",
                                        DigitalMopKeyUtils.TAG,
                                        isDataDogRequired = true,
                                        isError = false
                                    )

                                    _state.value = currentState.copy(
                                        isSyncing = false,
                                        lastSyncTime = System.currentTimeMillis()
                                    )
                                    // Refresh data after sync
                                    getOwnerInfo()
                                }
                                is SyncStatus.Failed -> {
                                    // Log sync failure
                                    analyticsLogger.logEventWithParameter(
                                        AnalyticsEvent.EVENT_GROUP_DK.eventName,
                                        AnalyticsEventParam.DK_SYNC_FAILED
                                    )

                                    DigitalMopKeyUtils.appendLog(
                                        "Digital key sync failed: ${syncStatus.error}",
                                        DigitalMopKeyUtils.TAG,
                                        isDataDogRequired = true,
                                        isError = true
                                    )

                                    _state.value = ManageDigitalKeyGen1State.Error(
                                        message = syncStatus.error,
                                        errorType = ErrorType.SYNC_FAILED,
                                        canRetry = true
                                    )
                                }
                                is SyncStatus.InProgress -> {
                                    // Keep syncing state
                                }
                                is SyncStatus.Idle -> {
                                    // Do nothing
                                }
                            }
                        }
                    }
                } catch (e: Exception) {
                    _state.value = ManageDigitalKeyGen1State.Error(
                        message = e.message ?: "Sync failed",
                        errorType = ErrorType.SYNC_FAILED,
                        canRetry = true
                    )
                }
            }
        }

        fun onRemoveKeyClick() {
            viewModelScope.launch {
                try {
                    val currentState = _state.value
                    if (currentState is ManageDigitalKeyGen1State.Success) {
                        _state.value = currentState.copy(isDeleting = true)

                        // Determine the type of key deletion based on owner status
                        val keyType = if (currentState.isEligibleForSharing) {
                            KeyRevocationType.PRIMARY // Owner key
                        } else {
                            KeyRevocationType.LUK_SELF // Shared key
                        }

                        // Create deletion request - VIN would come from application data
                        val deletionRequest = KeyDeletionRequest(
                            vin = currentState.vehicle?.vin ?: "",
                            keyInfoId = null, // Will be determined by repository
                            keyType = keyType,
                            isSelfPersona = true,
                            isSharedVehicle = !currentState.isEligibleForSharing
                        )

                        deleteDigitalKeyUseCase(deletionRequest).collect { status ->
                            when (status) {
                                is DigitalKeyStatus.Success -> {
                                    // Log successful deletion
                                    analyticsLogger.logEventWithParameter(
                                        AnalyticsEvent.EVENT_GROUP_DK.eventName,
                                        if (currentState.isEligibleForSharing) {
                                            AnalyticsEventParam.DK_DELETE_OWNER_KEY_SUCCESS
                                        } else {
                                            AnalyticsEventParam.DK_DELETE_SHARED_KEY_SUCCESS
                                        }
                                    )

                                    DigitalMopKeyUtils.appendLog(
                                        "Digital key deleted successfully. Type: ${keyType.type}",
                                        DigitalMopKeyUtils.DK_REVOKE_TAG,
                                        isDataDogRequired = true,
                                        isError = false
                                    )

                                    // Key deleted successfully, refresh data
                                    getOwnerInfo()
                                }
                                is DigitalKeyStatus.Error -> {
                                    // Log deletion failure
                                    analyticsLogger.logEventWithParameter(
                                        AnalyticsEvent.EVENT_GROUP_DK.eventName,
                                        AnalyticsEventParam.DK_REVOKE_KEY_FAILED
                                    )

                                    DigitalMopKeyUtils.appendLog(
                                        "Digital key deletion failed: ${status.message}",
                                        DigitalMopKeyUtils.DK_REVOKE_TAG,
                                        isDataDogRequired = true,
                                        isError = true
                                    )

                                    _state.value = ManageDigitalKeyGen1State.Error(
                                        message = status.message ?: "Failed to delete digital key",
                                        errorType = ErrorType.KEY_DELETION_FAILED,
                                        canRetry = true
                                    )
                                }
                                is DigitalKeyStatus.Loading -> {
                                    // Keep deleting state
                                }
                            }
                        }
                    }
                } catch (e: Exception) {
                    _state.value = ManageDigitalKeyGen1State.Error(
                        message = e.message ?: "Failed to delete digital key",
                        errorType = ErrorType.KEY_DELETION_FAILED,
                        canRetry = true
                    )
                }
            }
        }

        fun onHowToUseClick(activity: Activity) {
            // Log how to use click
            analyticsLogger.logEventWithParameter(
                AnalyticsEvent.EVENT_GROUP_DK.eventName,
                AnalyticsEventParam.DK_HOW_TO_USE_CLICKED
            )

            DigitalMopKeyUtils.appendLog(
                "How to use digital key clicked",
                DigitalMopKeyUtils.TAG,
                isDataDogRequired = true,
                isError = false
            )

            activity.startActivity(Intent(activity, DkOnBoardingActivity::class.java))
        }

        /**
         * Refreshes the digital key data
         */
        fun refreshData() {
            getOwnerInfo()
        }

        /**
         * Revokes a shared key for a specific user
         */
        fun revokeSharedKey(invite: LuksStatusInvite) {
            viewModelScope.launch {
                try {
                    val currentState = _state.value
                    if (currentState is ManageDigitalKeyGen1State.Success) {
                        _state.value = currentState.copy(isDeleting = true)

                        val deletionRequest = KeyDeletionRequest(
                            vin = invite.vin,
                            keyInfoId = invite.keyInfoId,
                            keyType = KeyRevocationType.PRIMARY, // Shared key revocation
                            inviteId = invite.id,
                            isSelfPersona = false,
                            isSharedVehicle = false
                        )

                        deleteDigitalKeyUseCase(deletionRequest).collect { status ->
                            when (status) {
                                is DigitalKeyStatus.Success -> {
                                    // Log successful shared key revocation
                                    analyticsLogger.logEventWithParameter(
                                        AnalyticsEvent.EVENT_GROUP_DK.eventName,
                                        AnalyticsEventParam.DK_REVOKE_SHARED_KEY_SUCCESS
                                    )

                                    DigitalMopKeyUtils.appendLog(
                                        "Shared key revoked successfully for user: ${invite.name}",
                                        DigitalMopKeyUtils.DK_REVOKE_TAG,
                                        isDataDogRequired = true,
                                        isError = false
                                    )

                                    // Shared key revoked successfully, refresh data
                                    getOwnerInfo()
                                }
                                is DigitalKeyStatus.Error -> {
                                    // Log shared key revocation failure
                                    analyticsLogger.logEventWithParameter(
                                        AnalyticsEvent.EVENT_GROUP_DK.eventName,
                                        AnalyticsEventParam.DK_REVOKE_KEY_FAILED
                                    )

                                    DigitalMopKeyUtils.appendLog(
                                        "Shared key revocation failed for user: ${invite.name}, Error: ${status.message}",
                                        DigitalMopKeyUtils.DK_REVOKE_TAG,
                                        isDataDogRequired = true,
                                        isError = true
                                    )

                                    _state.value = ManageDigitalKeyGen1State.Error(
                                        message = status.message ?: "Failed to revoke shared key",
                                        errorType = ErrorType.KEY_DELETION_FAILED,
                                        canRetry = true
                                    )
                                }
                                is DigitalKeyStatus.Loading -> {
                                    // Keep deleting state
                                }
                            }
                        }
                    }
                } catch (e: Exception) {
                    _state.value = ManageDigitalKeyGen1State.Error(
                        message = e.message ?: "Failed to revoke shared key",
                        errorType = ErrorType.KEY_DELETION_FAILED,
                        canRetry = true
                    )
                }
            }
        }

        /**
         * Retries the last failed operation
         */
        fun retry() {
            getOwnerInfo()
        }

        /**
         * Clears the current error state
         */
        fun clearError() {
            val currentState = _state.value
            if (currentState is ManageDigitalKeyGen1State.Error) {
                _state.value = ManageDigitalKeyGen1State.Loading
                getOwnerInfo()
            }
        }

        /**
         * Logs an event with parameter for analytics
         */
        fun logEventWithParam(event: String, param: String) {
            analyticsLogger.logEventWithParameter(event, param)
        }

        /**
         * Logs a general message to DataDog
         */
        fun logMessage(message: String, isError: Boolean = false) {
            DigitalMopKeyUtils.appendLog(
                message,
                DigitalMopKeyUtils.TAG,
                isDataDogRequired = true,
                isError = isError
            )
        }
    }
