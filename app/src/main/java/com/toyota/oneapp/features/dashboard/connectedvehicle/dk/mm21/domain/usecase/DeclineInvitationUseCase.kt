/*
* Copyright © 2025. Toyota Motors North America Inc
* All rights reserved.
*/

package com.toyota.oneapp.features.dashboard.connectedvehicle.dk.mm21.domain.usecase

import com.toyota.oneapp.features.dashboard.connectedvehicle.dk.mm21.domain.model.InvitationProcessResult
import com.toyota.oneapp.features.dashboard.connectedvehicle.dk.mm21.domain.model.PendingInviteTask
import com.toyota.oneapp.features.dashboard.connectedvehicle.dk.mm21.domain.repository.DigitalKeyGen1Repository
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import javax.inject.Inject

/**
 * Use case for declining a digital key invitation
 */
class DeclineInvitationUseCase @Inject constructor(
    private val repository: DigitalKeyGen1Repository
) {
    
    /**
     * Declines a pending digital key invitation
     * @param inviteTask The pending invitation task to decline
     * @return Flow of InvitationProcessResult
     */
    operator fun invoke(inviteTask: PendingInviteTask): Flow<InvitationProcessResult> = flow {
        try {
            // Decline the shared key invitation on the server
            val result = repository.declineSharedKeyInvitation(inviteTask.id)
            
            if (result.isSuccess) {
                // Clear pending invites
                repository.clearPendingInvites()
                
                // Log analytics event
                repository.logInvitationDeclined()
                
                emit(InvitationProcessResult.Declined)
            } else {
                emit(InvitationProcessResult.Failed(
                    result.exceptionOrNull()?.message ?: "Failed to decline invitation"
                ))
            }
        } catch (e: Exception) {
            emit(InvitationProcessResult.Failed(e.message ?: "Failed to decline invitation"))
        }
    }
}
