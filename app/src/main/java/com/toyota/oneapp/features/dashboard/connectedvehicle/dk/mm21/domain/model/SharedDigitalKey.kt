/*
* Copyright © 2025. Toyota Motors North America Inc
* All rights reserved.
*/

package com.toyota.oneapp.features.dashboard.connectedvehicle.dk.mm21.domain.model

data class SharedDigitalKey(
    val id: String,
    val keyInfoId: String,
    val name: String,
    val status: String,
    val phoneNumber: String,
    val vin: String,
    val keyType: String,
    val inviteId: String? = null,
    val timestamp: Long? = null,
    val isActive: Boolean = status == InvitationStatus.ACTIVE.status
) {
    companion object {
        const val MAX_SHARED_KEYS = 7
    }

    /**
     * Converts to LuksStatusInvite for backward compatibility
     */
    fun toLuksStatusInvite() = com.toyota.oneapp.features.dashboard.connectedvehicle.dk.domain.model.LuksStatusInvite(
        id = id,
        keyInfoId = keyInfoId,
        name = name,
        status = status,
        phoneNo = phoneNumber,
        vin = vin,
        keyType = keyType
    )
}
