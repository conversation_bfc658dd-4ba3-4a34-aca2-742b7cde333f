/*
* Copyright © 2025. Toyota Motors North America Inc
* All rights reserved.
*/

package com.toyota.oneapp.features.dashboard.connectedvehicle.dk.mm21.domain.model

import com.toyota.oneapp.digitalkey.DigitalkeyCalibration

/**
 * Represents the calibration status of a digital key
 */
data class CalibrationStatus(
    val isManualSetting: Boolean = false,
    val digitalkeyCalibration: DigitalkeyCalibration? = null,
    val keyInfoId: String? = null
) {
    companion object {
        fun default() = CalibrationStatus()
    }
}

/**
 * Represents calibration levels for digital key range
 */
enum class CalibrationLevel(val value: Int, val displayName: String) {
    LEVEL_1(1, "Close Range"),
    LEVEL_2(2, "Medium Range"),
    LEVEL_3(3, "Far Range"),
    LEVEL_4(4, "Maximum Range");

    companion object {
        fun fromValue(value: Int): CalibrationLevel? {
            return values().find { it.value == value }
        }
    }
}

/**
 * Represents the result of a calibration operation
 */
sealed class CalibrationResult {
    object Success : CalibrationResult()
    data class Failed(val error: String) : CalibrationResult()
}
