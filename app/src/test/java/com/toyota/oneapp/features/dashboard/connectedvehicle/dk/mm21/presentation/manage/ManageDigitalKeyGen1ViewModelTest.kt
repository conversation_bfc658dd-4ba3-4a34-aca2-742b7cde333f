/*
* Copyright © 2025. Toyota Motors North America Inc
* All rights reserved.
*/

package com.toyota.oneapp.features.dashboard.connectedvehicle.dk.mm21.presentation.manage

import androidx.arch.core.executor.testing.InstantTaskExecutorRule
import com.toyota.oneapp.analytics.AnalyticsLogger
import com.toyota.oneapp.app.DataDogUtils
import com.toyota.oneapp.features.dashboard.connectedvehicle.dk.mm21.domain.model.DigitalKeyOwner
import com.toyota.oneapp.features.dashboard.connectedvehicle.dk.mm21.domain.model.SharedDigitalKey
import com.toyota.oneapp.features.dashboard.connectedvehicle.dk.mm21.domain.usecase.DeleteDigitalKeyUseCase
import com.toyota.oneapp.features.dashboard.connectedvehicle.dk.mm21.domain.usecase.GetDigitalKeyOwnerUseCase
import com.toyota.oneapp.features.dashboard.connectedvehicle.dk.mm21.domain.usecase.GetSharedKeysUseCase
import com.toyota.oneapp.features.dashboard.connectedvehicle.dk.mm21.domain.usecase.SyncDigitalKeysUseCase
import io.mockk.coEvery
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.test.StandardTestDispatcher
import kotlinx.coroutines.test.advanceUntilIdle
import kotlinx.coroutines.test.resetMain
import kotlinx.coroutines.test.runTest
import kotlinx.coroutines.test.setMain
import org.junit.After
import org.junit.Assert.assertEquals
import org.junit.Assert.assertTrue
import org.junit.Before
import org.junit.Rule
import org.junit.Test

@ExperimentalCoroutinesApi
class ManageDigitalKeyGen1ViewModelTest {

    @get:Rule
    val instantTaskExecutorRule = InstantTaskExecutorRule()

    private val testDispatcher = StandardTestDispatcher()

    private lateinit var getDigitalKeyOwnerUseCase: GetDigitalKeyOwnerUseCase
    private lateinit var getSharedKeysUseCase: GetSharedKeysUseCase
    private lateinit var deleteDigitalKeyUseCase: DeleteDigitalKeyUseCase
    private lateinit var syncDigitalKeysUseCase: SyncDigitalKeysUseCase
    private lateinit var analyticsLogger: AnalyticsLogger
    private lateinit var dataDogUtils: DataDogUtils
    private lateinit var viewModel: ManageDigitalKeyGen1ViewModel

    @Before
    fun setup() {
        Dispatchers.setMain(testDispatcher)
        
        getDigitalKeyOwnerUseCase = mockk()
        getSharedKeysUseCase = mockk()
        deleteDigitalKeyUseCase = mockk()
        syncDigitalKeysUseCase = mockk()
        analyticsLogger = mockk(relaxed = true)
        dataDogUtils = mockk(relaxed = true)

        // Mock the static DigitalMopKeyUtils.appendLog method
        every { analyticsLogger.logEventWithParameter(any(), any(), any()) } returns Unit
    }

    @After
    fun tearDown() {
        Dispatchers.resetMain()
    }

    private fun createViewModel(): ManageDigitalKeyGen1ViewModel {
        return ManageDigitalKeyGen1ViewModel(
            getDigitalKeyOwnerUseCase = getDigitalKeyOwnerUseCase,
            getSharedKeysUseCase = getSharedKeysUseCase,
            deleteDigitalKeyUseCase = deleteDigitalKeyUseCase,
            syncDigitalKeysUseCase = syncDigitalKeysUseCase,
            analyticsLogger = analyticsLogger,
            dataDogUtils = dataDogUtils
        )
    }

    @Test
    fun `init should load owner info successfully`() = runTest {
        // Given
        val mockOwner = DigitalKeyOwner(
            firstName = "John",
            lastName = "Doe",
            isOwner = true,
            keyType = "OWNER"
        )
        val mockSharedKeys = listOf(
            SharedDigitalKey(
                id = "1",
                keyInfoId = "key1",
                name = "Jane Doe",
                status = "ACTIVE",
                phoneNumber = "1234567890",
                vin = "test-vin",
                keyType = "SHARED"
            )
        )

        coEvery { getDigitalKeyOwnerUseCase() } returns mockOwner
        coEvery { getSharedKeysUseCase() } returns mockSharedKeys

        // When
        viewModel = createViewModel()
        advanceUntilIdle()

        // Then
        val state = viewModel.state.value
        assertTrue(state is ManageDigitalKeyGen1State.Success)
        
        val successState = state as ManageDigitalKeyGen1State.Success
        assertEquals("John Doe", successState.ownerName)
        assertEquals(true, successState.isEligibleForSharing)
        assertEquals(true, successState.canInviteUsers)
        assertEquals(1, successState.sharedKeys?.size)
        
        verify { analyticsLogger.logEventWithParameter(any(), any(), any()) }
    }

    @Test
    fun `init should handle error when loading owner info fails`() = runTest {
        // Given
        val exception = RuntimeException("Network error")
        coEvery { getDigitalKeyOwnerUseCase() } throws exception

        // When
        viewModel = createViewModel()
        advanceUntilIdle()

        // Then
        val state = viewModel.state.value
        assertTrue(state is ManageDigitalKeyGen1State.Error)
        
        val errorState = state as ManageDigitalKeyGen1State.Error
        assertEquals("Network error", errorState.message)
        assertEquals(ErrorType.NETWORK, errorState.errorType)
    }

    @Test
    fun `refreshData should reload owner info`() = runTest {
        // Given
        val mockOwner = DigitalKeyOwner(
            firstName = "John",
            lastName = "Doe",
            isOwner = true,
            keyType = "OWNER"
        )
        coEvery { getDigitalKeyOwnerUseCase() } returns mockOwner
        coEvery { getSharedKeysUseCase() } returns emptyList()

        viewModel = createViewModel()
        advanceUntilIdle()

        // When
        viewModel.refreshData()
        advanceUntilIdle()

        // Then
        val state = viewModel.state.value
        assertTrue(state is ManageDigitalKeyGen1State.Success)
        
        // Verify that the use cases were called again
        coEvery { getDigitalKeyOwnerUseCase() }
        coEvery { getSharedKeysUseCase() }
    }

    @Test
    fun `retry should reload data after error`() = runTest {
        // Given
        val exception = RuntimeException("Network error")
        coEvery { getDigitalKeyOwnerUseCase() } throws exception

        viewModel = createViewModel()
        advanceUntilIdle()

        // Verify error state
        assertTrue(viewModel.state.value is ManageDigitalKeyGen1State.Error)

        // Mock successful retry
        val mockOwner = DigitalKeyOwner(
            firstName = "John",
            lastName = "Doe",
            isOwner = true,
            keyType = "OWNER"
        )
        coEvery { getDigitalKeyOwnerUseCase() } returns mockOwner
        coEvery { getSharedKeysUseCase() } returns emptyList()

        // When
        viewModel.retry()
        advanceUntilIdle()

        // Then
        val state = viewModel.state.value
        assertTrue(state is ManageDigitalKeyGen1State.Success)
    }

    @Test
    fun `logEventWithParam should call analytics logger`() {
        // Given
        viewModel = createViewModel()
        val event = "test_event"
        val param = "test_param"

        // When
        viewModel.logEventWithParam(event, param)

        // Then
        verify { analyticsLogger.logEventWithParameter(event, param, any()) }
    }
}
