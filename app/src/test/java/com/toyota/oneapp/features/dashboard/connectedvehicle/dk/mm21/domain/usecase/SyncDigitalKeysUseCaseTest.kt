/*
* Copyright © 2025. Toyota Motors North America Inc
* All rights reserved.
*/

package com.toyota.oneapp.features.dashboard.connectedvehicle.dk.mm21.domain.usecase

import com.toyota.oneapp.features.dashboard.connectedvehicle.dk.mm21.domain.model.SyncStatus
import com.toyota.oneapp.features.dashboard.connectedvehicle.dk.mm21.domain.repository.DigitalKeyGen1Repository
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.test.runTest
import org.junit.Assert.assertEquals
import org.junit.Assert.assertTrue
import org.junit.Before
import org.junit.Test

class SyncDigitalKeysUseCaseTest {

    private lateinit var repository: DigitalKeyGen1Repository
    private lateinit var useCase: SyncDigitalKeysUseCase

    @Before
    fun setup() {
        repository = mockk()
        useCase = SyncDigitalKeysUseCase(repository)
    }

    @Test
    fun `invoke should return completed when sync is successful`() = runTest {
        // Given
        coEvery { repository.syncAllKeys() } returns Result.success(Unit)

        // When
        val result = useCase().first()

        // Then
        assertTrue(result is SyncStatus.Completed)
        coVerify { repository.syncAllKeys() }
    }

    @Test
    fun `invoke should return failed when sync fails`() = runTest {
        // Given
        val exception = RuntimeException("Sync failed")
        coEvery { repository.syncAllKeys() } returns Result.failure(exception)

        // When
        val result = useCase().first()

        // Then
        assertTrue(result is SyncStatus.Failed)
        assertEquals("Sync failed", (result as SyncStatus.Failed).error)
        coVerify { repository.syncAllKeys() }
    }

    @Test
    fun `invoke should return failed when exception is thrown`() = runTest {
        // Given
        val exception = RuntimeException("Unexpected error")
        coEvery { repository.syncAllKeys() } throws exception

        // When
        val result = useCase().first()

        // Then
        assertTrue(result is SyncStatus.Failed)
        assertEquals("Unexpected error", (result as SyncStatus.Failed).error)
    }

    @Test
    fun `syncSpecificKey should return completed when sync is successful`() = runTest {
        // Given
        val keyInfoId = "test-key-id"
        coEvery { repository.syncKey(keyInfoId) } returns Result.success(Unit)

        // When
        val result = useCase.syncSpecificKey(keyInfoId).first()

        // Then
        assertTrue(result is SyncStatus.Completed)
        coVerify { repository.syncKey(keyInfoId) }
    }

    @Test
    fun `syncSpecificKey should return failed when sync fails`() = runTest {
        // Given
        val keyInfoId = "test-key-id"
        val exception = RuntimeException("Key sync failed")
        coEvery { repository.syncKey(keyInfoId) } returns Result.failure(exception)

        // When
        val result = useCase.syncSpecificKey(keyInfoId).first()

        // Then
        assertTrue(result is SyncStatus.Failed)
        assertEquals("Key sync failed", (result as SyncStatus.Failed).error)
        coVerify { repository.syncKey(keyInfoId) }
    }

    @Test
    fun `syncSpecificKey should return failed when exception is thrown`() = runTest {
        // Given
        val keyInfoId = "test-key-id"
        val exception = RuntimeException("Unexpected error")
        coEvery { repository.syncKey(keyInfoId) } throws exception

        // When
        val result = useCase.syncSpecificKey(keyInfoId).first()

        // Then
        assertTrue(result is SyncStatus.Failed)
        assertEquals("Unexpected error", (result as SyncStatus.Failed).error)
    }
}
