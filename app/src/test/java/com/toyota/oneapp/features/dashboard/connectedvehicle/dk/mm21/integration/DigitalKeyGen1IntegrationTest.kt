/*
* Copyright © 2025. Toyota Motors North America Inc
* All rights reserved.
*/

package com.toyota.oneapp.features.dashboard.connectedvehicle.dk.mm21.integration

import com.toyota.oneapp.features.dashboard.connectedvehicle.dk.mm21.domain.model.DigitalKeyOwner
import com.toyota.oneapp.features.dashboard.connectedvehicle.dk.mm21.domain.model.DigitalKeyStatus
import com.toyota.oneapp.features.dashboard.connectedvehicle.dk.mm21.domain.model.KeyDeletionRequest
import com.toyota.oneapp.features.dashboard.connectedvehicle.dk.mm21.domain.model.KeyRevocationResult
import com.toyota.oneapp.features.dashboard.connectedvehicle.dk.mm21.domain.model.KeyRevocationType
import com.toyota.oneapp.features.dashboard.connectedvehicle.dk.mm21.domain.model.SharedDigitalKey
import com.toyota.oneapp.features.dashboard.connectedvehicle.dk.mm21.domain.model.SyncStatus
import com.toyota.oneapp.features.dashboard.connectedvehicle.dk.mm21.domain.repository.DigitalKeyGen1Repository
import com.toyota.oneapp.features.dashboard.connectedvehicle.dk.mm21.domain.usecase.DeleteDigitalKeyUseCase
import com.toyota.oneapp.features.dashboard.connectedvehicle.dk.mm21.domain.usecase.GetDigitalKeyOwnerUseCase
import com.toyota.oneapp.features.dashboard.connectedvehicle.dk.mm21.domain.usecase.GetSharedKeysUseCase
import com.toyota.oneapp.features.dashboard.connectedvehicle.dk.mm21.domain.usecase.SyncDigitalKeysUseCase
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.test.runTest
import org.junit.Assert.assertEquals
import org.junit.Assert.assertTrue
import org.junit.Before
import org.junit.Test

/**
 * Integration tests to validate the complete flow from use cases to repository
 */
class DigitalKeyGen1IntegrationTest {

    private lateinit var repository: DigitalKeyGen1Repository
    private lateinit var getDigitalKeyOwnerUseCase: GetDigitalKeyOwnerUseCase
    private lateinit var getSharedKeysUseCase: GetSharedKeysUseCase
    private lateinit var deleteDigitalKeyUseCase: DeleteDigitalKeyUseCase
    private lateinit var syncDigitalKeysUseCase: SyncDigitalKeysUseCase

    @Before
    fun setup() {
        repository = mockk()
        getDigitalKeyOwnerUseCase = GetDigitalKeyOwnerUseCase(repository)
        getSharedKeysUseCase = GetSharedKeysUseCase(repository)
        deleteDigitalKeyUseCase = DeleteDigitalKeyUseCase(repository)
        syncDigitalKeysUseCase = SyncDigitalKeysUseCase(repository)
    }

    @Test
    fun `complete flow - get owner and shared keys successfully`() = runTest {
        // Given
        val mockOwner = DigitalKeyOwner(
            firstName = "John",
            lastName = "Doe",
            isOwner = true,
            keyType = "OWNER"
        )
        val mockSharedKeys = listOf(
            SharedDigitalKey(
                id = "1",
                keyInfoId = "key1",
                name = "Jane Doe",
                status = "ACTIVE",
                phoneNumber = "1234567890",
                vin = "test-vin",
                keyType = "SHARED"
            )
        )

        coEvery { repository.getVinKeyInfo() } returns mockk {
            every { keyKind } returns jp.co.denso.dklib.DKLib.KeyKind.OWNER
        }
        coEvery { repository.getUserInfo() } returns mockk {
            every { firstName } returns "John"
            every { lastName } returns "Doe"
        }
        coEvery { repository.getSharedKeys() } returns mockSharedKeys

        // When
        val ownerResult = getDigitalKeyOwnerUseCase()
        val sharedKeysResult = getSharedKeysUseCase()

        // Then
        assertEquals("John", ownerResult.firstName)
        assertEquals("Doe", ownerResult.lastName)
        assertTrue(ownerResult.isOwner)
        assertEquals(1, sharedKeysResult.size)
        assertEquals("Jane Doe", sharedKeysResult[0].name)
    }

    @Test
    fun `complete flow - delete digital key successfully`() = runTest {
        // Given
        val deletionRequest = KeyDeletionRequest(
            vin = "test-vin",
            keyInfoId = "test-key-id",
            keyType = KeyRevocationType.PRIMARY,
            isSelfPersona = true,
            isSharedVehicle = false
        )

        coEvery { repository.deleteOwnerKey(deletionRequest) } returns KeyRevocationResult.Success
        coEvery { repository.clearInviteSize(deletionRequest.vin) } returns Unit
        coEvery { repository.removeLocalKey(deletionRequest.vin, deletionRequest.isSharedVehicle) } returns Unit

        // When
        val result = deleteDigitalKeyUseCase(deletionRequest).first()

        // Then
        assertTrue(result is DigitalKeyStatus.Success)
        coVerify { repository.deleteOwnerKey(deletionRequest) }
        coVerify { repository.clearInviteSize(deletionRequest.vin) }
        coVerify { repository.removeLocalKey(deletionRequest.vin, deletionRequest.isSharedVehicle) }
    }

    @Test
    fun `complete flow - sync digital keys successfully`() = runTest {
        // Given
        coEvery { repository.syncAllKeys() } returns Result.success(Unit)

        // When
        val result = syncDigitalKeysUseCase().first()

        // Then
        assertTrue(result is SyncStatus.Completed)
        coVerify { repository.syncAllKeys() }
    }

    @Test
    fun `error handling - repository failure propagates correctly`() = runTest {
        // Given
        val deletionRequest = KeyDeletionRequest(
            vin = "test-vin",
            keyInfoId = "test-key-id",
            keyType = KeyRevocationType.PRIMARY,
            isSelfPersona = true,
            isSharedVehicle = false
        )

        val errorMessage = "Network connection failed"
        coEvery { repository.deleteOwnerKey(deletionRequest) } returns KeyRevocationResult.Failed(errorMessage, 500)

        // When
        val result = deleteDigitalKeyUseCase(deletionRequest).first()

        // Then
        assertTrue(result is DigitalKeyStatus.Error)
        assertEquals(errorMessage, (result as DigitalKeyStatus.Error).message)
        assertEquals(500, result.code)
    }

    @Test
    fun `error handling - sync failure propagates correctly`() = runTest {
        // Given
        val errorMessage = "Sync service unavailable"
        coEvery { repository.syncAllKeys() } returns Result.failure(RuntimeException(errorMessage))

        // When
        val result = syncDigitalKeysUseCase().first()

        // Then
        assertTrue(result is SyncStatus.Failed)
        assertEquals(errorMessage, (result as SyncStatus.Failed).error)
    }
}
