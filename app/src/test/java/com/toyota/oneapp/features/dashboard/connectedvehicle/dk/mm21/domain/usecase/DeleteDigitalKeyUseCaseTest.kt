/*
* Copyright © 2025. Toyota Motors North America Inc
* All rights reserved.
*/

package com.toyota.oneapp.features.dashboard.connectedvehicle.dk.mm21.domain.usecase

import com.toyota.oneapp.features.dashboard.connectedvehicle.dk.mm21.domain.model.DigitalKeyStatus
import com.toyota.oneapp.features.dashboard.connectedvehicle.dk.mm21.domain.model.KeyDeletionRequest
import com.toyota.oneapp.features.dashboard.connectedvehicle.dk.mm21.domain.model.KeyRevocationResult
import com.toyota.oneapp.features.dashboard.connectedvehicle.dk.mm21.domain.model.KeyRevocationType
import com.toyota.oneapp.features.dashboard.connectedvehicle.dk.mm21.domain.repository.DigitalKeyGen1Repository
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.test.runTest
import org.junit.Assert.assertEquals
import org.junit.Assert.assertTrue
import org.junit.Before
import org.junit.Test

class DeleteDigitalKeyUseCaseTest {

    private lateinit var repository: DigitalKeyGen1Repository
    private lateinit var useCase: DeleteDigitalKeyUseCase

    @Before
    fun setup() {
        repository = mockk()
        useCase = DeleteDigitalKeyUseCase(repository)
    }

    @Test
    fun `invoke with PRIMARY key type should call deleteOwnerKey and return success`() = runTest {
        // Given
        val request = KeyDeletionRequest(
            vin = "test-vin",
            keyInfoId = "test-key-id",
            keyType = KeyRevocationType.PRIMARY,
            isSelfPersona = true,
            isSharedVehicle = false
        )
        
        coEvery { repository.deleteOwnerKey(request) } returns KeyRevocationResult.Success
        coEvery { repository.clearInviteSize(request.vin) } returns Unit
        coEvery { repository.removeLocalKey(request.vin, request.isSharedVehicle) } returns Unit

        // When
        val result = useCase(request).first()

        // Then
        assertTrue(result is DigitalKeyStatus.Success)
        coVerify { repository.deleteOwnerKey(request) }
        coVerify { repository.clearInviteSize(request.vin) }
        coVerify { repository.removeLocalKey(request.vin, request.isSharedVehicle) }
    }

    @Test
    fun `invoke with LUK_SELF key type should call deleteOwnerKey and return success`() = runTest {
        // Given
        val request = KeyDeletionRequest(
            vin = "test-vin",
            keyInfoId = "test-key-id",
            keyType = KeyRevocationType.LUK_SELF,
            isSelfPersona = true,
            isSharedVehicle = false
        )
        
        coEvery { repository.deleteOwnerKey(request) } returns KeyRevocationResult.Success
        coEvery { repository.removeLocalKey(request.vin, request.isSharedVehicle) } returns Unit

        // When
        val result = useCase(request).first()

        // Then
        assertTrue(result is DigitalKeyStatus.Success)
        coVerify { repository.deleteOwnerKey(request) }
        coVerify { repository.removeLocalKey(request.vin, request.isSharedVehicle) }
    }

    @Test
    fun `invoke with PRIMARY_LUK key type should call deactivateLocalKey and return success`() = runTest {
        // Given
        val request = KeyDeletionRequest(
            vin = "test-vin",
            keyInfoId = "test-key-id",
            keyType = KeyRevocationType.PRIMARY_LUK,
            isSelfPersona = true,
            isSharedVehicle = false
        )
        
        coEvery { repository.deactivateLocalKey(request) } returns KeyRevocationResult.Success
        coEvery { repository.syncKey("test-key-id") } returns Result.success(Unit)

        // When
        val result = useCase(request).first()

        // Then
        assertTrue(result is DigitalKeyStatus.Success)
        coVerify { repository.deactivateLocalKey(request) }
        coVerify { repository.syncKey("test-key-id") }
    }

    @Test
    fun `invoke should return error when repository operation fails`() = runTest {
        // Given
        val request = KeyDeletionRequest(
            vin = "test-vin",
            keyInfoId = "test-key-id",
            keyType = KeyRevocationType.PRIMARY,
            isSelfPersona = true,
            isSharedVehicle = false
        )
        
        val errorMessage = "Deletion failed"
        coEvery { repository.deleteOwnerKey(request) } returns KeyRevocationResult.Failed(errorMessage, 500)

        // When
        val result = useCase(request).first()

        // Then
        assertTrue(result is DigitalKeyStatus.Error)
        assertEquals(errorMessage, (result as DigitalKeyStatus.Error).message)
        assertEquals(500, result.code)
    }

    @Test
    fun `invoke should return error when exception is thrown`() = runTest {
        // Given
        val request = KeyDeletionRequest(
            vin = "test-vin",
            keyInfoId = "test-key-id",
            keyType = KeyRevocationType.PRIMARY,
            isSelfPersona = true,
            isSharedVehicle = false
        )
        
        val exception = RuntimeException("Unexpected error")
        coEvery { repository.deleteOwnerKey(request) } throws exception

        // When
        val result = useCase(request).first()

        // Then
        assertTrue(result is DigitalKeyStatus.Error)
        assertEquals("Unexpected error", (result as DigitalKeyStatus.Error).message)
    }
}
