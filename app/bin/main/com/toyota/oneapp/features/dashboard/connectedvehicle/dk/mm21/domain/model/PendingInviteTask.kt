/*
* Copyright © 2025. Toyota Motors North America Inc
* All rights reserved.
*/

package com.toyota.oneapp.features.dashboard.connectedvehicle.dk.mm21.domain.model

/**
 * Represents a pending invitation task that requires user action
 */
data class PendingInviteTask(
    val id: String,
    val vin: String,
    val inviterName: String,
    val vehicleName: String,
    val status: String,
    val timestamp: Long,
    val phoneNumber: String? = null
) {
    val isActionRequired: <PERSON><PERSON>an
        get() = status == InvitationStatus.SMS_SENT.status
}

/**
 * Represents the result of processing a pending invitation
 */
sealed class InvitationProcessResult {
    object Accepted : InvitationProcessResult()
    object Declined : InvitationProcessResult()
    data class Failed(val error: String) : InvitationProcessResult()
}

/**
 * Represents an invitation action
 */
enum class InvitationAction {
    ACCEPT,
    DECLINE
}
