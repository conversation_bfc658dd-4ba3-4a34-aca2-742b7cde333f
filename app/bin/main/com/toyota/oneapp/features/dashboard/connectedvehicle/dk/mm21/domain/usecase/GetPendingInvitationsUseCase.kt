/*
* Copyright © 2025. Toyota Motors North America Inc
* All rights reserved.
*/

package com.toyota.oneapp.features.dashboard.connectedvehicle.dk.mm21.domain.usecase

import com.toyota.oneapp.features.dashboard.connectedvehicle.dk.mm21.domain.model.InvitationStatus
import com.toyota.oneapp.features.dashboard.connectedvehicle.dk.mm21.domain.model.PendingInviteTask
import com.toyota.oneapp.features.dashboard.connectedvehicle.dk.mm21.domain.repository.DigitalKeyGen1Repository
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import javax.inject.Inject

/**
 * Use case for getting pending digital key invitations
 */
class GetPendingInvitationsUseCase @Inject constructor(
    private val repository: DigitalKeyGen1Repository
) {
    
    /**
     * Gets all pending invitations that require user action
     * @return Flow of List<PendingInviteTask>
     */
    operator fun invoke(): Flow<List<PendingInviteTask>> = flow {
        try {
            // Fetch pending invites from server
            val result = repository.fetchPendingInvites()
            
            if (result.isSuccess) {
                val pendingInvites = repository.getLocalPendingInvites()
                
                // Filter for invites that require action (SMS_SENT status)
                val actionableInvites = pendingInvites.filter { invite ->
                    invite.status == InvitationStatus.SMS_SENT.status
                }
                
                emit(actionableInvites)
            } else {
                // If server fetch fails, return local pending invites
                val localInvites = repository.getLocalPendingInvites()
                emit(localInvites.filter { it.isActionRequired })
            }
        } catch (e: Exception) {
            // Return empty list on error, but could also emit error state
            emit(emptyList())
        }
    }
    
    /**
     * Gets pending invitations for a specific VIN
     * @param vin The vehicle identification number
     * @return Flow of List<PendingInviteTask>
     */
    fun getForVin(vin: String): Flow<List<PendingInviteTask>> = flow {
        try {
            val allPendingInvites = repository.getLocalPendingInvites()
            val vinSpecificInvites = allPendingInvites.filter { it.vin == vin }
            emit(vinSpecificInvites)
        } catch (e: Exception) {
            emit(emptyList())
        }
    }
    
    /**
     * Checks if there are any pending invitations that need user attention
     * @return Flow of Boolean
     */
    fun hasPendingInvitations(): Flow<Boolean> = flow {
        try {
            val pendingInvites = repository.getLocalPendingInvites()
            val hasActionableInvites = pendingInvites.any { it.isActionRequired }
            emit(hasActionableInvites)
        } catch (e: Exception) {
            emit(false)
        }
    }
}
