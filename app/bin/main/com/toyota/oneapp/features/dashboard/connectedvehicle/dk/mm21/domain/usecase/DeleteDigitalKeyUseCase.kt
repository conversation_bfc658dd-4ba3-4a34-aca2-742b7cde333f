/*
* Copyright © 2025. Toyota Motors North America Inc
* All rights reserved.
*/

package com.toyota.oneapp.features.dashboard.connectedvehicle.dk.mm21.domain.usecase

import com.toyota.oneapp.features.dashboard.connectedvehicle.dk.mm21.domain.model.DigitalKeyStatus
import com.toyota.oneapp.features.dashboard.connectedvehicle.dk.mm21.domain.model.KeyDeletionRequest
import com.toyota.oneapp.features.dashboard.connectedvehicle.dk.mm21.domain.model.KeyRevocationResult
import com.toyota.oneapp.features.dashboard.connectedvehicle.dk.mm21.domain.model.KeyRevocationType
import com.toyota.oneapp.features.dashboard.connectedvehicle.dk.mm21.domain.repository.DigitalKeyGen1Repository
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import javax.inject.Inject

/**
 * Use case for deleting digital keys (both owner and shared keys)
 */
class DeleteDigitalKeyUseCase @Inject constructor(
    private val repository: DigitalKeyGen1Repository
) {
    
    /**
     * Deletes a digital key based on the deletion request
     * @param request The key deletion request containing all necessary information
     * @return Flow of DigitalKeyStatus representing the operation status
     */
    operator fun invoke(request: KeyDeletionRequest): Flow<DigitalKeyStatus> = flow {
        emit(DigitalKeyStatus.Loading)
        
        try {
            val result = when (request.keyType) {
                KeyRevocationType.PRIMARY -> {
                    if (request.isSelfPersona) {
                        repository.deleteOwnerKey(request)
                    } else {
                        repository.revokeSharedKey(request)
                    }
                }
                KeyRevocationType.LUK_SELF -> {
                    repository.deleteOwnerKey(request)
                }
                KeyRevocationType.PRIMARY_LUK -> {
                    repository.deactivateLocalKey(request)
                }
            }
            
            when (result) {
                is KeyRevocationResult.Success -> {
                    // Handle post-deletion cleanup based on key type
                    handlePostDeletionCleanup(request)
                    emit(DigitalKeyStatus.Success)
                }
                is KeyRevocationResult.Failed -> {
                    emit(DigitalKeyStatus.Error(result.error, result.code))
                }
            }
        } catch (e: Exception) {
            emit(DigitalKeyStatus.Error(e.message ?: "Unknown error occurred"))
        }
    }
    
    private suspend fun handlePostDeletionCleanup(request: KeyDeletionRequest) {
        when (request.keyType) {
            KeyRevocationType.PRIMARY -> {
                if (request.isSelfPersona) {
                    repository.clearInviteSize(request.vin)
                    repository.removeLocalKey(request.vin, request.isSharedVehicle)
                }
            }
            KeyRevocationType.LUK_SELF -> {
                repository.removeLocalKey(request.vin, request.isSharedVehicle)
            }
            KeyRevocationType.PRIMARY_LUK -> {
                request.keyInfoId?.let { keyInfoId ->
                    repository.syncKey(keyInfoId)
                }
            }
        }
    }
}
