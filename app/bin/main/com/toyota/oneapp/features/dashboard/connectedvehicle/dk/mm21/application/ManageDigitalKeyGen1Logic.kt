/*
* Copyright © 2025. Toyota Motors North America Inc
* All rights reserved.
*/

package com.toyota.oneapp.features.dashboard.connectedvehicle.dk.mm21.application

import com.toyota.oneapp.analytics.AnalyticsEvent
import com.toyota.oneapp.analytics.AnalyticsEventParam
import com.toyota.oneapp.analytics.AnalyticsLogger
import com.toyota.oneapp.app.ApplicationData
import com.toyota.oneapp.digitalkey.DigitalKeyLocalData
import com.toyota.oneapp.digitalkey.DigitalMopKeyUtils
import com.toyota.oneapp.features.dashboard.connectedvehicle.dk.domain.model.LuksStatusInvite
import com.toyota.oneapp.features.dashboard.connectedvehicle.dk.mm21.domain.model.DigitalKeyOwner
import com.toyota.oneapp.features.dashboard.connectedvehicle.dk.mm21.domain.model.SharedDigitalKey
import com.toyota.oneapp.features.dashboard.connectedvehicle.dk.mm21.domain.repository.DigitalKeyGen1Repository
import com.toyota.oneapp.features.dashboard.connectedvehicle.dk.mm21.presentation.state.DigitalKeyState
import com.toyota.oneapp.network.Resource
import com.toyota.oneapp.sharedpref.OneAppPreferenceModel
import jp.co.denso.dklib.DKLib
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import javax.inject.Inject

interface ManageDigitalKeyGen1UseCase {
    suspend fun getOwnerInfo(): Flow<DigitalKeyState<DigitalKeyOwner>>
    suspend fun getSharedKeys(): Flow<DigitalKeyState<List<SharedDigitalKey>>>
    suspend fun deleteOwnerKey(): Flow<DigitalKeyState<Boolean>>
    suspend fun revokeSharedKey(invite: LuksStatusInvite): Flow<DigitalKeyState<Boolean>>
    suspend fun syncKeys(): Flow<DigitalKeyState<Boolean>>
    fun logAnalyticsEvent(event: String, param: String)
}

class ManageDigitalKeyGen1Logic
    @Inject
    constructor(
        private val applicationData: ApplicationData,
        private val digitalMopKeyUtils: DigitalMopKeyUtils,
        private val digitalKeyLocalData: DigitalKeyLocalData,
        private val analyticsLogger: AnalyticsLogger,
        private val repository: DigitalKeyGen1Repository,
        private val digitalKeyGen1UseCase: DigitalKeyGen1UseCase,
        private val oneAppPreferenceModel: OneAppPreferenceModel,
    ) : ManageDigitalKeyGen1UseCase {

    override suspend fun getOwnerInfo(): Flow<DigitalKeyState<DigitalKeyOwner>> = flow {
        try {
            val keyInfo = repository.getVinKeyInfo()
            val userInfo = repository.getUserInfo()
            
            if (keyInfo != null && userInfo != null) {
                val owner = DigitalKeyOwner(
                    firstName = userInfo.firstName ?: "",
                    lastName = userInfo.lastName ?: "",
                    isOwner = keyInfo.keyKind == DKLib.KeyKind.OWNER,
                    keyType = keyInfo.keyKind.name
                )
                
                DigitalMopKeyUtils.appendLog(
                    "Owner info loaded successfully: ${owner.firstName} ${owner.lastName}, isOwner: ${owner.isOwner}",
                    DigitalMopKeyUtils.TAG,
                    isDataDogRequired = true,
                    isError = false
                )
                
                emit(DigitalKeyState.Success(owner))
            } else {
                emit(DigitalKeyState.Error(null, "Failed to load owner information"))
            }
        } catch (e: Exception) {
            DigitalMopKeyUtils.appendLog(
                "Failed to load owner info: ${e.message}",
                DigitalMopKeyUtils.TAG,
                isDataDogRequired = true,
                isError = true
            )
            emit(DigitalKeyState.Error(null, e.message ?: "Unknown error"))
        }
    }

    override suspend fun getSharedKeys(): Flow<DigitalKeyState<List<SharedDigitalKey>>> = flow {
        try {
            val sharedKeys = repository.getSharedKeys()
            
            DigitalMopKeyUtils.appendLog(
                "Shared keys loaded: ${sharedKeys.size} keys",
                DigitalMopKeyUtils.TAG,
                isDataDogRequired = true,
                isError = false
            )
            
            emit(DigitalKeyState.Success(sharedKeys))
        } catch (e: Exception) {
            DigitalMopKeyUtils.appendLog(
                "Failed to load shared keys: ${e.message}",
                DigitalMopKeyUtils.TAG,
                isDataDogRequired = true,
                isError = true
            )
            emit(DigitalKeyState.Error(null, e.message ?: "Failed to load shared keys"))
        }
    }

    override suspend fun deleteOwnerKey(): Flow<DigitalKeyState<Boolean>> = flow {
        try {
            val vehicle = applicationData.getSelectedVehicle()
            val keyInfo = repository.getVinKeyInfo()
            
            if (vehicle != null && keyInfo != null) {
                // Log the deletion attempt
                analyticsLogger.logEventWithParameter(
                    AnalyticsEvent.EVENT_GROUP_DK.eventName,
                    AnalyticsEventParam.DK_DELETE_OWNER_KEY_ATTEMPT
                )
                
                // Determine deletion type based on key kind
                val isOwner = keyInfo.keyKind == DKLib.KeyKind.OWNER
                
                if (isOwner) {
                    // Owner key deletion
                    val deviceId = digitalKeyLocalData.getDeviceId()
                    val phoneNumber = digitalKeyLocalData.getPhoneNumber()
                    
                    if (deviceId != null && phoneNumber != null) {
                        // Call the deletion API through the use case
                        val result = digitalKeyGen1UseCase.deleteDigitalKeyToken(
                            keyInfoId = keyInfo.keyInfoId,
                            phoneNumber = phoneNumber,
                            vin = vehicle.vin,
                            deviceId = deviceId,
                            type = "PRIMARY",
                            inviteId = null
                        )
                        
                        // Process the result
                        result.collect { state ->
                            when (state) {
                                is DigitalKeyState.Success -> {
                                    // Clean up local data
                                    digitalKeyLocalData.setInviteSize(vehicle.vin, 0)
                                    digitalMopKeyUtils.removeKey(vehicle.vin, oneAppPreferenceModel)
                                    
                                    analyticsLogger.logEventWithParameter(
                                        AnalyticsEvent.EVENT_GROUP_DK.eventName,
                                        AnalyticsEventParam.DK_DELETE_OWNER_KEY_SUCCESS
                                    )
                                    
                                    DigitalMopKeyUtils.appendLog(
                                        "Owner key deleted successfully",
                                        DigitalMopKeyUtils.DK_REVOKE_TAG,
                                        isDataDogRequired = true,
                                        isError = false
                                    )
                                    
                                    emit(DigitalKeyState.Success(true))
                                }
                                is DigitalKeyState.Error -> {
                                    analyticsLogger.logEventWithParameter(
                                        AnalyticsEvent.EVENT_GROUP_DK.eventName,
                                        AnalyticsEventParam.DK_REVOKE_KEY_FAILED
                                    )
                                    
                                    DigitalMopKeyUtils.appendLog(
                                        "Owner key deletion failed: ${state.message}",
                                        DigitalMopKeyUtils.DK_REVOKE_TAG,
                                        isDataDogRequired = true,
                                        isError = true
                                    )
                                    
                                    emit(DigitalKeyState.Error(state.code, state.message ?: "Failed to delete owner key"))
                                }
                                else -> {
                                    // Loading state, continue waiting
                                }
                            }
                        }
                    } else {
                        emit(DigitalKeyState.Error(null, "Device ID or phone number not available"))
                    }
                } else {
                    // Shared key deletion (LUK_SELF)
                    digitalMopKeyUtils.removeKey(vehicle.vin, oneAppPreferenceModel)
                    
                    analyticsLogger.logEventWithParameter(
                        AnalyticsEvent.EVENT_GROUP_DK.eventName,
                        AnalyticsEventParam.DK_DELETE_SHARED_KEY_SUCCESS
                    )
                    
                    DigitalMopKeyUtils.appendLog(
                        "Shared key deleted successfully",
                        DigitalMopKeyUtils.DK_REVOKE_TAG,
                        isDataDogRequired = true,
                        isError = false
                    )
                    
                    emit(DigitalKeyState.Success(true))
                }
            } else {
                emit(DigitalKeyState.Error(null, "Vehicle or key information not available"))
            }
        } catch (e: Exception) {
            analyticsLogger.logEventWithParameter(
                AnalyticsEvent.EVENT_GROUP_DK.eventName,
                AnalyticsEventParam.DK_REVOKE_KEY_FAILED
            )
            
            DigitalMopKeyUtils.appendLog(
                "Key deletion failed: ${e.message}",
                DigitalMopKeyUtils.DK_REVOKE_TAG,
                isDataDogRequired = true,
                isError = true
            )
            
            emit(DigitalKeyState.Error(null, e.message ?: "Failed to delete key"))
        }
    }

    override suspend fun revokeSharedKey(invite: LuksStatusInvite): Flow<DigitalKeyState<Boolean>> = flow {
        try {
            analyticsLogger.logEventWithParameter(
                AnalyticsEvent.EVENT_GROUP_DK.eventName,
                AnalyticsEventParam.DK_REVOKE_SHARED_KEY_ATTEMPT
            )
            
            val deviceId = digitalKeyLocalData.getDeviceId()
            
            if (deviceId != null) {
                // Call the revocation API
                val result = digitalKeyGen1UseCase.deleteDigitalKeyToken(
                    keyInfoId = invite.keyInfoId,
                    phoneNumber = invite.phoneNo,
                    vin = invite.vin,
                    deviceId = deviceId,
                    type = invite.keyType,
                    inviteId = invite.id
                )
                
                result.collect { state ->
                    when (state) {
                        is DigitalKeyState.Success -> {
                            // Remove from local storage
                            digitalKeyLocalData.removeInvite(invite.phoneNo)
                            
                            analyticsLogger.logEventWithParameter(
                                AnalyticsEvent.EVENT_GROUP_DK.eventName,
                                AnalyticsEventParam.DK_REVOKE_SHARED_KEY_SUCCESS
                            )
                            
                            DigitalMopKeyUtils.appendLog(
                                "Shared key revoked successfully for user: ${invite.name}",
                                DigitalMopKeyUtils.DK_REVOKE_TAG,
                                isDataDogRequired = true,
                                isError = false
                            )
                            
                            emit(DigitalKeyState.Success(true))
                        }
                        is DigitalKeyState.Error -> {
                            analyticsLogger.logEventWithParameter(
                                AnalyticsEvent.EVENT_GROUP_DK.eventName,
                                AnalyticsEventParam.DK_REVOKE_KEY_FAILED
                            )
                            
                            DigitalMopKeyUtils.appendLog(
                                "Shared key revocation failed for user: ${invite.name}, Error: ${state.message}",
                                DigitalMopKeyUtils.DK_REVOKE_TAG,
                                isDataDogRequired = true,
                                isError = true
                            )
                            
                            emit(DigitalKeyState.Error(state.code, state.message ?: "Failed to revoke shared key"))
                        }
                        else -> {
                            // Loading state, continue waiting
                        }
                    }
                }
            } else {
                emit(DigitalKeyState.Error(null, "Device ID not available"))
            }
        } catch (e: Exception) {
            analyticsLogger.logEventWithParameter(
                AnalyticsEvent.EVENT_GROUP_DK.eventName,
                AnalyticsEventParam.DK_REVOKE_KEY_FAILED
            )
            
            DigitalMopKeyUtils.appendLog(
                "Shared key revocation failed: ${e.message}",
                DigitalMopKeyUtils.DK_REVOKE_TAG,
                isDataDogRequired = true,
                isError = true
            )
            
            emit(DigitalKeyState.Error(null, e.message ?: "Failed to revoke shared key"))
        }
    }

    override suspend fun syncKeys(): Flow<DigitalKeyState<Boolean>> = flow {
        try {
            analyticsLogger.logEventWithParameter(
                AnalyticsEvent.EVENT_GROUP_DK.eventName,
                AnalyticsEventParam.DK_SYNC_ATTEMPT
            )
            
            digitalMopKeyUtils.syncAllKeys(oneAppPreferenceModel)
            
            if (digitalMopKeyUtils.isSyncCompleted) {
                analyticsLogger.logEventWithParameter(
                    AnalyticsEvent.EVENT_GROUP_DK.eventName,
                    AnalyticsEventParam.DK_SYNC_SUCCESS
                )
                
                DigitalMopKeyUtils.appendLog(
                    "Digital key sync completed successfully",
                    DigitalMopKeyUtils.TAG,
                    isDataDogRequired = true,
                    isError = false
                )
                
                emit(DigitalKeyState.Success(true))
            } else {
                analyticsLogger.logEventWithParameter(
                    AnalyticsEvent.EVENT_GROUP_DK.eventName,
                    AnalyticsEventParam.DK_SYNC_FAILED
                )
                
                DigitalMopKeyUtils.appendLog(
                    "Digital key sync failed",
                    DigitalMopKeyUtils.TAG,
                    isDataDogRequired = true,
                    isError = true
                )
                
                emit(DigitalKeyState.Error(null, "Sync not completed"))
            }
        } catch (e: Exception) {
            analyticsLogger.logEventWithParameter(
                AnalyticsEvent.EVENT_GROUP_DK.eventName,
                AnalyticsEventParam.DK_SYNC_FAILED
            )
            
            DigitalMopKeyUtils.appendLog(
                "Digital key sync failed: ${e.message}",
                DigitalMopKeyUtils.TAG,
                isDataDogRequired = true,
                isError = true
            )
            
            emit(DigitalKeyState.Error(null, e.message ?: "Sync failed"))
        }
    }

    override fun logAnalyticsEvent(event: String, param: String) {
        analyticsLogger.logEventWithParameter(event, param)
    }
}
