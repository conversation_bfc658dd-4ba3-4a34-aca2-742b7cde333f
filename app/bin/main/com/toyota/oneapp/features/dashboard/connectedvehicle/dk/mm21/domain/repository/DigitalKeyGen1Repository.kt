/*
* Copyright © 2025. Toyota Motors North America Inc
* All rights reserved.
*/

package com.toyota.oneapp.features.dashboard.connectedvehicle.dk.mm21.domain.repository

import android.net.Uri
import com.toyota.oneapp.features.core.commonapicalls.dataaccess.servermodel.ProfileInfoCustomer
import com.toyota.oneapp.features.dashboard.connectedvehicle.dk.mm21.domain.model.CalibrationLevel
import com.toyota.oneapp.features.dashboard.connectedvehicle.dk.mm21.domain.model.CalibrationSetting
import com.toyota.oneapp.features.dashboard.connectedvehicle.dk.mm21.domain.model.CalibrationStatus
import com.toyota.oneapp.features.dashboard.connectedvehicle.dk.mm21.domain.model.ContactInfo
import com.toyota.oneapp.features.dashboard.connectedvehicle.dk.mm21.domain.model.DigitalKeyInviteRequest
import com.toyota.oneapp.features.dashboard.connectedvehicle.dk.mm21.domain.model.KeyDeletionRequest
import com.toyota.oneapp.features.dashboard.connectedvehicle.dk.mm21.domain.model.KeyRevocationResult
import com.toyota.oneapp.features.dashboard.connectedvehicle.dk.mm21.domain.model.PendingInviteTask
import com.toyota.oneapp.features.dashboard.connectedvehicle.dk.mm21.domain.model.SharedDigitalKey
import com.toyota.oneapp.features.dashboard.connectedvehicle.dk.mm21.domain.model.SharedKeyRevocationRequest
import com.toyota.oneapp.features.dashboard.connectedvehicle.dk.mm21.domain.model.SyncStatus
import com.toyota.oneapp.model.vehicle.VehicleInfo
import jp.co.denso.dklib.DKLib
import kotlinx.coroutines.flow.Flow

interface DigitalKeyGen1Repository {
    suspend fun getVinKeyInfo(): DKLib.MyKeyInfo?

    suspend fun getUserInfo(): ProfileInfoCustomer?

    suspend fun getSharedKeys(): List<SharedDigitalKey>

    suspend fun getContactDetails(uri: Uri): ContactInfo?

    suspend fun inviteUser(request: DigitalKeyInviteRequest): Result<Unit>

    suspend fun getCalibrationSetting(): CalibrationSetting

    suspend fun setCalibrationLevel(level: CalibrationLevel): Result<Unit>

    suspend fun toggleManualSetting(enabled: Boolean): Result<Unit>

    suspend fun isBluetoothEnabled(): Boolean

    // Key deletion and revocation operations
    suspend fun deleteOwnerKey(request: KeyDeletionRequest): KeyRevocationResult
    suspend fun revokeSharedKey(request: KeyDeletionRequest): KeyRevocationResult
    suspend fun deactivateLocalKey(request: KeyDeletionRequest): KeyRevocationResult
    suspend fun revokeSharedKeyFromCTP(request: SharedKeyRevocationRequest): Result<Unit>

    // Synchronization operations
    suspend fun syncAllKeys(): Result<Unit>
    suspend fun syncKey(keyInfoId: String): Result<Unit>
    fun getSyncStatus(): Flow<SyncStatus>
    fun getPendingOperations(): Flow<List<String>>
    fun getLastSyncTime(): Flow<Long?>

    // Local data management
    suspend fun clearInviteSize(vin: String)
    suspend fun removeLocalKey(vin: String, isSharedVehicle: Boolean)
    suspend fun removeLocalInvite(phoneNumber: String)

    // Calibration management
    suspend fun getCalibrationStatus(keyInfoId: String): CalibrationStatus
    suspend fun setBleCalibration(keyInfo: DKLib.MyKeyInfo, freqValue: Int): Result<Unit>
    suspend fun updateCalibrationStatus(keyInfoId: String, status: CalibrationStatus)
    suspend fun reconnectBluetooth()

    // Invitation management
    suspend fun fetchPendingInvites(): Result<Unit>
    suspend fun getLocalPendingInvites(): List<PendingInviteTask>
    suspend fun clearPendingInvites()
    suspend fun declineSharedKeyInvitation(inviteId: String): Result<Unit>

    // Vehicle management
    suspend fun getVehicleInfo(vin: String): VehicleInfo?
    suspend fun setVehicleDigitalKeyStatus(vin: String, hasDigitalKey: Boolean)
    suspend fun updateVehicleInList(vehicleInfo: VehicleInfo)
    suspend fun setSelectedVehicle(vehicleInfo: VehicleInfo)
    suspend fun updateSelectedVehicleDigitalKeyStatus(hasDigitalKey: Boolean)
    suspend fun setLocalVehicleInfo(vehicleInfo: VehicleInfo)
    suspend fun isRemoteOnlyUser(): Boolean

    // Status and analytics
    suspend fun setSecondaryDkAcceptedStatus(accepted: Boolean)
    suspend fun logInvitationAccepted()
    suspend fun logInvitationDeclined()
}
