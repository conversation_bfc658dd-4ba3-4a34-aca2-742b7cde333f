/*
* Copyright © 2025. Toyota Motors North America Inc
* All rights reserved.
*/

package com.toyota.oneapp.features.dashboard.connectedvehicle.dk.mm21.domain.usecase

import com.toyota.oneapp.features.dashboard.connectedvehicle.dk.mm21.domain.model.DigitalKeyOperationState
import com.toyota.oneapp.features.dashboard.connectedvehicle.dk.mm21.domain.model.SyncStatus
import com.toyota.oneapp.features.dashboard.connectedvehicle.dk.mm21.domain.repository.DigitalKeyGen1Repository
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.combine
import javax.inject.Inject

/**
 * Use case for getting the current status of digital key operations
 */
class GetKeyStatusUseCase @Inject constructor(
    private val repository: DigitalKeyGen1Repository
) {
    
    /**
     * Gets the current digital key operation state
     * @return Flow of DigitalKeyOperationState
     */
    operator fun invoke(): Flow<DigitalKeyOperationState> {
        return combine(
            repository.getSyncStatus(),
            repository.getPendingOperations(),
            repository.getLastSyncTime()
        ) { syncStatus, pendingOps, lastSyncTime ->
            DigitalKeyOperationState(
                isLoading = syncStatus == SyncStatus.InProgress,
                lastSyncTime = lastSyncTime,
                syncStatus = syncStatus,
                pendingOperations = pendingOps,
                error = if (syncStatus is SyncStatus.Failed) syncStatus.error else null
            )
        }
    }
    
    /**
     * Checks if there are any pending operations
     * @return Flow of Boolean indicating if operations are pending
     */
    fun hasPendingOperations(): Flow<Boolean> {
        return repository.getPendingOperations().let { flow ->
            combine(flow, repository.getSyncStatus()) { pendingOps, syncStatus ->
                pendingOps.isNotEmpty() || syncStatus == SyncStatus.InProgress
            }
        }
    }
}
