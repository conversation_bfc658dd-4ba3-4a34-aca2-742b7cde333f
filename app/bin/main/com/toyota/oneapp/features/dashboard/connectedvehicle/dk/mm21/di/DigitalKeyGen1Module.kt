package com.toyota.oneapp.features.dashboard.connectedvehicle.dk.mm21.di

import com.toyota.oneapp.features.dashboard.connectedvehicle.dk.mm21.application.ManageDigitalKeyGen1Logic
import com.toyota.oneapp.features.dashboard.connectedvehicle.dk.mm21.application.ManageDigitalKeyGen1UseCase
import com.toyota.oneapp.features.dashboard.connectedvehicle.dk.mm21.data.repository.DigitalKeyGen1RepositoryImpl
import com.toyota.oneapp.features.dashboard.connectedvehicle.dk.mm21.domain.repository.DigitalKeyGen1Repository
import dagger.Binds
import dagger.Module
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

@Module
@InstallIn(SingletonComponent::class)
abstract class DigitalKeyGen1Module {
    @Binds
    @Singleton
    abstract fun bindDigitalKeyGen1Repository(repository: DigitalKeyGen1RepositoryImpl): DigitalKeyGen1Repository

    @Binds
    abstract fun bindManageDigitalKeyGen1UseCase(
        manageDigitalKeyGen1Logic: ManageDigitalKeyGen1Logic,
    ): ManageDigitalKeyGen1UseCase
}
