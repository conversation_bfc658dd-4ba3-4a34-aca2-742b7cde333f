/*
* Copyright © 2024. Toyota Motors North America Inc
* All rights reserved.
*/

package com.toyota.oneapp.features.dashboard.connectedvehicle.remotetab.dashboardtile.presentation

import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.RowScope
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.ExperimentalMaterialApi
import androidx.compose.material.LinearProgressIndicator
import androidx.compose.material.ProgressIndicatorDefaults
import androidx.compose.material.Surface
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.layout.layoutId
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import androidx.fragment.app.FragmentActivity
import com.toyota.oneapp.features.core.theme.AppTheme
import com.toyota.oneapp.features.core.util.AccessibilityId
import com.toyota.oneapp.features.dashboard.connectedvehicle.dk.domain.model.ButtonState
import com.toyota.oneapp.features.dashboard.connectedvehicle.dk.domain.model.DigitalKeyGen1DownloadState
import com.toyota.oneapp.features.dashboard.connectedvehicle.dk.mm21.presentation.state.DigitalKeyEvent
import com.toyota.oneapp.features.dashboard.connectedvehicle.remotetab.dashboardtile.domain.model.RemoteTile

@Composable
fun DisplayGen1DigitalKeyScreen(
    remoteTile: RemoteTile,
    state: DigitalKeyGen1DownloadState,
    onEvent: (DigitalKeyEvent) -> Unit,
) {
    Box(
        modifier = Modifier.fillMaxWidth(),
        contentAlignment = Alignment.Center,
    ) {
        Row(
            modifier =
                Modifier
                    .padding(12.dp)
                    .fillMaxWidth(),
            verticalAlignment = Alignment.CenterVertically,
        ) {
            Spacer(modifier = Modifier.weight(1f))
            when (state.buttonState) {
                ButtonState.PRE_DOWNLOAD -> DkGen1DownloadCompose(onEvent, remoteTile)
                ButtonState.DOWNLOADING -> {
                    DkGen1DownloadProgressCompose(state)
                }
                else -> {
                    // do nothing
                }
            }
        }
    }
}

@Composable
fun DkGen1DownloadProgressCompose(state: DigitalKeyGen1DownloadState) {
    val animatedProgress =
        animateFloatAsState(
            targetValue = state.downloadProgressInPercent / 100f,
            animationSpec = ProgressIndicatorDefaults.ProgressAnimationSpec,
            label = "",
        ).value
    Box(
        modifier =
            Modifier
                .width(72.dp)
                .background(
                    color = AppTheme.colors.secondary02,
                    shape = RoundedCornerShape(5.dp),
                ),
        contentAlignment = Alignment.CenterStart,
    ) {
        LinearProgressIndicator(
            modifier =
                Modifier
                    .fillMaxWidth()
                    .layoutId(AccessibilityId.DK_WIDGET_DOWNLOAD_IN_PROGRESS_VIEW),
            progress = animatedProgress,
            color = AppTheme.colors.secondary01,
        )
    }
}

@OptIn(ExperimentalMaterialApi::class)
@Composable
private fun RowScope.DkGen1DownloadCompose(
    onEvent: (DigitalKeyEvent) -> Unit,
    remoteTile: RemoteTile,
) {
    val context = LocalContext.current
    val activity = LocalContext.current as FragmentActivity

    Surface(
        modifier =
            Modifier
                .wrapContentSize()
                .padding(end = 4.dp)
                .clip(shape = RoundedCornerShape(20.dp))
                .align(Alignment.CenterVertically),
        color = AppTheme.colors.tile01,
        border = BorderStroke(2.dp, AppTheme.colors.borderPrimary),
        shape = RoundedCornerShape(20.dp),
        onClick = {
            onEvent(DigitalKeyEvent.StartDownload(context, activity))
        },
    ) {
        remoteTile.type.mTitle?.let { title ->
            Text(
                text = stringResource(title),
                modifier = Modifier.padding(12.dp),
                color = AppTheme.colors.textPrimary,
            )
        }
    }
}
