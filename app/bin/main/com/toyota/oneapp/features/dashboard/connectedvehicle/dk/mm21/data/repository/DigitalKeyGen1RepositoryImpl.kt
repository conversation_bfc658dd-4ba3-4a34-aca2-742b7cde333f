/*
* Copyright © 2025. Toyota Motors North America Inc
* All rights reserved.
*/

package com.toyota.oneapp.features.dashboard.connectedvehicle.dk.mm21.data.repository

import android.bluetooth.BluetoothManager
import android.content.Context
import android.net.Uri
import android.provider.ContactsContract
import com.toyota.oneapp.analytics.AnalyticsEvent
import com.toyota.oneapp.analytics.AnalyticsEventParam
import com.toyota.oneapp.analytics.AnalyticsLogger
import com.toyota.oneapp.app.ApplicationData
import com.toyota.oneapp.app.DataDogUtils
import com.toyota.oneapp.digitalkey.DkCalibrationStatus
import com.toyota.oneapp.digitalkey.DigitalKeyLocalData
import com.toyota.oneapp.digitalkey.DigitalMopKeyUtils
import com.toyota.oneapp.digitalkey.DigitalkeyCalibration
import com.toyota.oneapp.features.core.commonapicalls.dataaccess.servermodel.ProfileInfoCustomer
import com.toyota.oneapp.features.dashboard.connectedvehicle.dk.application.DigitalKeyDownloadLogic
import com.toyota.oneapp.features.dashboard.connectedvehicle.dk.domain.DigitalKeyErrorCodeStatus
import com.toyota.oneapp.features.dashboard.connectedvehicle.dk.domain.DigitalKeyRevokeStatus
import com.toyota.oneapp.features.dashboard.connectedvehicle.dk.domain.model.DigitalKeyInviteRequest
import com.toyota.oneapp.features.dashboard.connectedvehicle.dk.domain.model.PendingInvite
import com.toyota.oneapp.features.dashboard.connectedvehicle.dk.mm21.domain.model.CalibrationLevel
import com.toyota.oneapp.features.dashboard.connectedvehicle.dk.mm21.domain.model.CalibrationSetting
import com.toyota.oneapp.features.dashboard.connectedvehicle.dk.mm21.domain.model.CalibrationStatus
import com.toyota.oneapp.features.dashboard.connectedvehicle.dk.mm21.domain.model.ContactInfo
import com.toyota.oneapp.features.dashboard.connectedvehicle.dk.mm21.domain.model.KeyDeletionRequest
import com.toyota.oneapp.features.dashboard.connectedvehicle.dk.mm21.domain.model.KeyRevocationResult
import com.toyota.oneapp.features.dashboard.connectedvehicle.dk.mm21.domain.model.PendingInviteTask
import com.toyota.oneapp.features.dashboard.connectedvehicle.dk.mm21.domain.model.SharedDigitalKey
import com.toyota.oneapp.features.dashboard.connectedvehicle.dk.mm21.domain.model.SharedKeyRevocationRequest
import com.toyota.oneapp.features.dashboard.connectedvehicle.dk.mm21.domain.model.SyncStatus
import com.toyota.oneapp.features.dashboard.connectedvehicle.dk.mm21.domain.repository.DigitalKeyGen1Repository
import com.toyota.oneapp.language.RegionManager
import com.toyota.oneapp.model.vehicle.VehicleInfo
import com.toyota.oneapp.network.Resource
import com.toyota.oneapp.sharedpref.OneAppPreferenceModel
import dagger.hilt.android.qualifiers.ApplicationContext
import jp.co.denso.dklib.DKLib
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.withContext
import javax.inject.Inject

class DigitalKeyGen1RepositoryImpl
    @Inject
    constructor(
        private val applicationData: ApplicationData,
        private val digitalMopKeyUtils: DigitalMopKeyUtils,
        private val oneAppPreferenceModel: OneAppPreferenceModel,
        @ApplicationContext private val context: Context,
        private val digitalKeyDownloadLogic: DigitalKeyDownloadLogic,
        private val regionManager: RegionManager,
        private val digitalKeyLocalData: DigitalKeyLocalData,
        private val analyticsLogger: AnalyticsLogger,
        private val dataDogUtils: DataDogUtils,
    ) : DigitalKeyGen1Repository {

        // State flows for tracking sync status and operations
        private val _syncStatus = MutableStateFlow<SyncStatus>(SyncStatus.Idle)
        private val _pendingOperations = MutableStateFlow<List<String>>(emptyList())
        private val _lastSyncTime = MutableStateFlow<Long?>(null)
        override suspend fun getVinKeyInfo(): DKLib.MyKeyInfo? =
            applicationData.getSelectedVehicle()?.vin?.let { vin ->
                digitalMopKeyUtils.getVinKeyInfo(vin, oneAppPreferenceModel)
            }

        override suspend fun getUserInfo(): ProfileInfoCustomer? {
            val keyInfo = getVinKeyInfo()
            return keyInfo?.keyInfoId?.let {
                ProfileInfoCustomer(
                    guid = oneAppPreferenceModel.getGuid(),
                    forgerockId = "",
                    customerType = "",
                    firstName = keyInfo.keyInfoId,
                    lastName = "",
                    phoneNumbers = emptyList(),
                    addresses = emptyList(),
                    emails = emptyList(),
                    accountStatus = "",
                    uiLanguage = "",
                    preferredLanguage = "",
                    updateUserId = "",
                    createSource = "",
                    lastUpdateSource = "",
                    createDate = 0,
                    lastUpdateDate = 0,
                )
            }
        }

        override suspend fun getSharedKeys(): List<SharedDigitalKey> {
            val currentVehicleVin = applicationData.getSelectedVehicle()?.vin ?: return emptyList()
            return digitalMopKeyUtils
                .getKeysInfo()
                .filter { keyInfo ->
                    // Filter keys that belong to the current vehicle and are not owner keys
                    digitalMopKeyUtils.getVehicleIdForKey(keyInfo.keyInfoId) == currentVehicleVin &&
                        keyInfo.keyKind != DKLib.KeyKind.OWNER
                }.map { keyInfo ->
                    SharedDigitalKey(
                        id = keyInfo.keyInfoId,
                        keyInfoId = keyInfo.keyInfoId,
                        name = keyInfo.keyInfoId,
                        // For now using keyInfoId as name
                        status = if (keyInfo.keyStatus == DKLib.KeyStatus.REGISTERED) "ACTIVE" else "PENDING",
                        phoneNumber = "",
                        // This might need to be retrieved from another source
                        vin = currentVehicleVin,
                        keyType = keyInfo.keyKind.toString(),
                    )
                }
        }

        override suspend fun inviteUser(
            request: com.toyota.oneapp.features.dashboard.connectedvehicle.dk.mm21.domain.model.DigitalKeyInviteRequest,
        ): Result<Unit> {
            return try {
                var deviceId = digitalKeyLocalData.getDeviceId()
                if (deviceId.isNullOrEmpty()) {
                    val response =
                        withContext(Dispatchers.IO) {
                            digitalMopKeyUtils.getDeviceId()
                        }
                    if (response.success) {
                        deviceId = response.appendString
                        digitalKeyLocalData.setDeviceId(deviceId)
                    }
                }

                val oldRequest =
                    DigitalKeyInviteRequest(
                        firstName = request.firstName,
                        lastName = request.lastName,
                        phone = request.phone,
                        countryCode = request.countryCode,
                        primaryGuid = request.primaryGuid.toString(),
                        primaryDeviceId = request.primaryDeviceId.toString(),
                        vin = request.vin,
                    )

                val guidTemp = digitalKeyLocalData.getGuid()
                val selectedVehicle = applicationData.getSelectedVehicle()

                selectedVehicle?.let { vehicleInfo ->
                    var result: Result<Unit>? = null

                    digitalKeyDownloadLogic.postDigitalKeyInvite(oldRequest).collect { resource ->
                        result =
                            when (resource) {
                                is Resource.Success -> Result.success(Unit)
                                is Resource.Failure -> {
                                    val error =
                                        when (resource.responseCode) {
                                            DigitalKeyErrorCodeStatus.DK_INVITE_KEY_GUEST_DOWNLOAD_ERROR_MSG_CODE.errorCode ->
                                                Exception("Guest already has a digital key")
                                            DigitalKeyErrorCodeStatus.DK_INVITE_KEY_GUEST_PAIRED_ERROR_MSG_CODE.errorCode ->
                                                Exception("Guest is already paired")
                                            else -> Exception("Failed to invite user")
                                        }
                                    Result.failure(error)
                                }
                                else -> Result.failure(Exception("Unknown error occurred"))
                            }
                    }

                    return result ?: Result.failure(Exception("Failed to process invite"))
                } ?: return Result.failure(Exception("No vehicle selected"))
            } catch (e: Exception) {
                return Result.failure(e)
            }
        }

        override suspend fun getContactDetails(uri: Uri): ContactInfo? {
            return try {
                val cursor =
                    context.contentResolver.query(
                        uri,
                        null,
                        null,
                        null,
                        null,
                    )

                cursor?.use { c ->
                    if (c.moveToFirst()) {
                        val id = c.getString(c.getColumnIndexOrThrow(ContactsContract.Contacts._ID))
                        val hasPhone = c.getString(c.getColumnIndexOrThrow(ContactsContract.Contacts.HAS_PHONE_NUMBER))

                        if (hasPhone.toInt() > 0) {
                            val phoneCursor =
                                context.contentResolver.query(
                                    ContactsContract.CommonDataKinds.Phone.CONTENT_URI,
                                    null,
                                    ContactsContract.CommonDataKinds.Phone.CONTACT_ID + " = ?",
                                    arrayOf(id),
                                    null,
                                )

                            phoneCursor?.use { pc ->
                                if (pc.moveToFirst()) {
                                    val phoneNumber =
                                        pc
                                            .getString(pc.getColumnIndexOrThrow(ContactsContract.CommonDataKinds.Phone.NUMBER))
                                            .replace("[^0-9]".toRegex(), "") // Remove non-numeric characters
                                            .takeLast(10) // Take last 10 digits

                                    // Get name details
                                    val nameCursor =
                                        context.contentResolver.query(
                                            ContactsContract.Data.CONTENT_URI,
                                            null,
                                            "${ContactsContract.Data.CONTACT_ID} = ? AND ${ContactsContract.Data.MIMETYPE} = ?",
                                            arrayOf(id, ContactsContract.CommonDataKinds.StructuredName.CONTENT_ITEM_TYPE),
                                            null,
                                        )

                                    nameCursor?.use { nc ->
                                        if (nc.moveToFirst()) {
                                            val firstName =
                                                nc.getString(
                                                    nc.getColumnIndexOrThrow(ContactsContract.CommonDataKinds.StructuredName.GIVEN_NAME),
                                                )
                                                    ?: ""
                                            val lastName =
                                                nc.getString(
                                                    nc.getColumnIndexOrThrow(ContactsContract.CommonDataKinds.StructuredName.FAMILY_NAME),
                                                )
                                                    ?: ""

                                            return ContactInfo(
                                                firstName = firstName,
                                                lastName = lastName,
                                                phoneNumber = phoneNumber,
                                            )
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
                null
            } catch (e: Exception) {
                throw e
            }
        }

        override suspend fun getCalibrationSetting(): CalibrationSetting =
            CalibrationSetting(
                isManualSetting = digitalMopKeyUtils.isManualCalibrationEnabled(),
                calibrationLevel =
                    when (digitalMopKeyUtils.getCalibrationLevel()) {
                        DigitalkeyCalibration.LOW -> CalibrationLevel.LOW
                        DigitalkeyCalibration.MEDIUM -> CalibrationLevel.MEDIUM
                        DigitalkeyCalibration.HIGH -> CalibrationLevel.HIGH
                        else -> CalibrationLevel.MEDIUM
                    },
            )

        override suspend fun setCalibrationLevel(level: CalibrationLevel): Result<Unit> =
            try {
                val calibration =
                    when (level) {
                        CalibrationLevel.LOW -> DigitalkeyCalibration.LOW
                        CalibrationLevel.MEDIUM -> DigitalkeyCalibration.MEDIUM
                        CalibrationLevel.HIGH -> DigitalkeyCalibration.HIGH
                    }
                digitalMopKeyUtils.setCalibrationLevel(calibration)
                Result.success(Unit)
            } catch (e: Exception) {
                Result.failure(e)
            }

        override suspend fun toggleManualSetting(enabled: Boolean): Result<Unit> =
            try {
                digitalMopKeyUtils.setManualCalibration(enabled)
                Result.success(Unit)
            } catch (e: Exception) {
                Result.failure(e)
            }

        override suspend fun isBluetoothEnabled(): Boolean {
            val manager = context.getSystemService(Context.BLUETOOTH_SERVICE) as BluetoothManager
            return manager.adapter?.isEnabled == true
        }

        // Key deletion and revocation operations
        override suspend fun deleteOwnerKey(request: KeyDeletionRequest): KeyRevocationResult {
            return try {
                val deviceId = digitalKeyLocalData.getDeviceId() ?: return KeyRevocationResult.Failed("Device ID not found")
                val phoneNumber = digitalKeyLocalData.getPhoneNumber()

                digitalKeyDownloadLogic.deleteDigitalKeyToken(
                    digitalKeyLocalData.getTmpKeyInfoId(request.vin),
                    phoneNumber,
                    request.vin,
                    deviceId,
                    request.keyType.type,
                    request.inviteId ?: ""
                ).collect { resource ->
                    resource.error?.let { status ->
                        if (status.code == 204 || status.code == 400) {
                            digitalKeyLocalData.setInviteSize(request.vin, 0)
                            digitalMopKeyUtils.removeKey(request.vin, oneAppPreferenceModel)
                        }
                    }
                }
                digitalKeyLocalData.removeTmpKeyInfoId(request.vin)
                KeyRevocationResult.Success
            } catch (e: Exception) {
                KeyRevocationResult.Failed(e.message ?: "Unknown error", null)
            }
        }

        override suspend fun revokeSharedKey(request: KeyDeletionRequest): KeyRevocationResult {
            return try {
                val deviceId = digitalKeyLocalData.getDeviceId() ?: return KeyRevocationResult.Failed("Device ID not found")
                val phoneNumber = digitalKeyLocalData.getPhoneNumber()

                digitalKeyDownloadLogic.deleteDigitalKeyToken(
                    request.keyInfoId,
                    phoneNumber,
                    request.vin,
                    deviceId,
                    request.keyType.type,
                    request.inviteId ?: ""
                ).collect { resource ->
                    resource.error?.let { status ->
                        if (status.code != 204) {
                            throw Exception("Failed to revoke shared key: ${status.code}")
                        }
                    }
                }
                KeyRevocationResult.Success
            } catch (e: Exception) {
                KeyRevocationResult.Failed(e.message ?: "Unknown error", null)
            }
        }

        override suspend fun deactivateLocalKey(request: KeyDeletionRequest): KeyRevocationResult {
            return try {
                request.keyInfoId?.let { keyInfoId ->
                    val response = digitalMopKeyUtils.deactivateKey(keyInfoId)
                    if (response.success) {
                        KeyRevocationResult.Success
                    } else {
                        KeyRevocationResult.Failed("Failed to deactivate local key")
                    }
                } ?: KeyRevocationResult.Failed("Key info ID is required")
            } catch (e: Exception) {
                KeyRevocationResult.Failed(e.message ?: "Unknown error")
            }
        }

        override suspend fun revokeSharedKeyFromCTP(request: SharedKeyRevocationRequest): Result<Unit> {
            return try {
                val deviceId = digitalKeyLocalData.getDeviceId() ?: return Result.failure(Exception("Device ID not found"))

                digitalKeyDownloadLogic.deleteDigitalKeyToken(
                    request.keyInfoId,
                    request.phoneNumber,
                    request.vin,
                    deviceId,
                    request.keyType,
                    request.inviteId
                ).collect { resource ->
                    resource.error?.let { status ->
                        if (status.code != 204) {
                            throw Exception("Failed to revoke shared key from CTP: ${status.code}")
                        }
                    }
                }
                Result.success(Unit)
            } catch (e: Exception) {
                Result.failure(e)
            }
        }

        // Synchronization operations
        override suspend fun syncAllKeys(): Result<Unit> {
            return try {
                _syncStatus.value = SyncStatus.InProgress
                digitalMopKeyUtils.syncAllKeys(oneAppPreferenceModel)

                if (digitalMopKeyUtils.isSyncCompleted) {
                    _syncStatus.value = SyncStatus.Completed
                    _lastSyncTime.value = System.currentTimeMillis()
                    Result.success(Unit)
                } else {
                    _syncStatus.value = SyncStatus.Failed("Sync not completed")
                    Result.failure(Exception("Sync not completed"))
                }
            } catch (e: Exception) {
                _syncStatus.value = SyncStatus.Failed(e.message ?: "Sync failed")
                Result.failure(e)
            }
        }

        override suspend fun syncKey(keyInfoId: String): Result<Unit> {
            return try {
                digitalMopKeyUtils.syncKey(keyInfoId, oneAppPreferenceModel)
                Result.success(Unit)
            } catch (e: Exception) {
                Result.failure(e)
            }
        }

        override fun getSyncStatus(): Flow<SyncStatus> = _syncStatus.asStateFlow()

        override fun getPendingOperations(): Flow<List<String>> = _pendingOperations.asStateFlow()

        override fun getLastSyncTime(): Flow<Long?> = _lastSyncTime.asStateFlow()

        // Local data management
        override suspend fun clearInviteSize(vin: String) {
            digitalKeyLocalData.setInviteSize(vin, 0)
        }

        override suspend fun removeLocalKey(vin: String, isSharedVehicle: Boolean) {
            digitalMopKeyUtils.removeKey(vin, oneAppPreferenceModel)
            if (isSharedVehicle) {
                // Additional cleanup for shared vehicles
                digitalKeyLocalData.clearPersonas(vin)
            }
        }

        override suspend fun removeLocalInvite(phoneNumber: String) {
            digitalKeyLocalData.removeInvite(phoneNumber)
        }

        // Calibration management
        override suspend fun getCalibrationStatus(keyInfoId: String): CalibrationStatus {
            val dkCalibrationStatus = digitalKeyLocalData.getCalibrationMap().getOrDefault(
                keyInfoId,
                DkCalibrationStatus()
            )
            return CalibrationStatus(
                isManualSetting = dkCalibrationStatus.isManualSetting,
                digitalkeyCalibration = dkCalibrationStatus.digitalkeyCalibration,
                keyInfoId = keyInfoId
            )
        }

        override suspend fun setBleCalibration(keyInfo: DKLib.MyKeyInfo, freqValue: Int): Result<Unit> {
            return try {
                val response = digitalMopKeyUtils.setCaliber(keyInfo, freqValue)
                if (response.success) {
                    Result.success(Unit)
                } else {
                    Result.failure(Exception("Failed to set BLE calibration"))
                }
            } catch (e: Exception) {
                Result.failure(e)
            }
        }

        override suspend fun updateCalibrationStatus(keyInfoId: String, status: CalibrationStatus) {
            val dkCalibrationStatus = DkCalibrationStatus(
                isManualSetting = status.isManualSetting,
                digitalkeyCalibration = status.digitalkeyCalibration ?: DigitalkeyCalibration.MEDIUM
            )
            digitalKeyLocalData.getCalibrationMap()[keyInfoId] = dkCalibrationStatus
        }

        override suspend fun reconnectBluetooth() {
            // Reconnect bluetooth to apply calibration settings
            digitalMopKeyUtils.dKLib?.setBleFunctionOff()
        }

        // Invitation management
        override suspend fun fetchPendingInvites(): Result<Unit> {
            return try {
                // This would typically fetch from server and update local storage
                // Implementation depends on the specific API structure
                Result.success(Unit)
            } catch (e: Exception) {
                Result.failure(e)
            }
        }

        override suspend fun getLocalPendingInvites(): List<PendingInviteTask> {
            return try {
                val pendingInvites = digitalKeyLocalData.getPendingInviteList()
                pendingInvites.mapNotNull { invite ->
                    invite?.let {
                        PendingInviteTask(
                            id = it.id,
                            vin = it.vin,
                            inviterName = "Unknown", // PendingInvite doesn't have inviterName
                            vehicleName = "Unknown Vehicle", // PendingInvite doesn't have vehicleName
                            status = it.status,
                            timestamp = System.currentTimeMillis(),
                            phoneNumber = null // PendingInvite doesn't have phoneNumber
                        )
                    }
                }
            } catch (e: Exception) {
                emptyList()
            }
        }

        override suspend fun clearPendingInvites() {
            digitalKeyLocalData.setPendingInviteList(emptyList())
        }

        override suspend fun declineSharedKeyInvitation(inviteId: String): Result<Unit> {
            return try {
                // Implementation would call the appropriate API to decline invitation
                // For now, just clear from local storage
                clearPendingInvites()
                Result.success(Unit)
            } catch (e: Exception) {
                Result.failure(e)
            }
        }

        // Vehicle management
        override suspend fun getVehicleInfo(vin: String): VehicleInfo? {
            return digitalKeyLocalData.getVehicle(vin)
        }

        override suspend fun setVehicleDigitalKeyStatus(vin: String, hasDigitalKey: Boolean) {
            val vehicleInfo = digitalKeyLocalData.getVehicle(vin)
            vehicleInfo?.setDigitalkey(hasDigitalKey)
        }

        override suspend fun updateVehicleInList(vehicleInfo: VehicleInfo) {
            val vehicleList = applicationData.getVehicleList() ?: ArrayList()
            vehicleList.add(vehicleInfo)
            applicationData.setVehicleList(vehicleList)
        }

        override suspend fun setSelectedVehicle(vehicleInfo: VehicleInfo) {
            applicationData.setSelectedVehicle(vehicleInfo)
        }

        override suspend fun updateSelectedVehicleDigitalKeyStatus(hasDigitalKey: Boolean) {
            applicationData.getSelectedVehicle()?.setDigitalkey(hasDigitalKey)
        }

        override suspend fun setLocalVehicleInfo(vehicleInfo: VehicleInfo) {
            digitalKeyLocalData.setVehicleInfo(vehicleInfo)
        }

        override suspend fun isRemoteOnlyUser(): Boolean {
            return applicationData.getSelectedVehicle()?.isRemoteOnlyUser == true
        }

        // Status and analytics
        override suspend fun setSecondaryDkAcceptedStatus(accepted: Boolean) {
            digitalMopKeyUtils.isSecondaryDkAccepted = accepted
            digitalMopKeyUtils.isSecondaryDkAcceptedState.value = accepted
        }

        override suspend fun logInvitationAccepted() {
            analyticsLogger.logEventWithParameter(
                AnalyticsEvent.EVENT_GROUP_DK.eventName,
                AnalyticsEventParam.VEHICLE_DIGITAL_KEY_SHARED_ACCEPT
            )
        }

        override suspend fun logInvitationDeclined() {
            analyticsLogger.logEventWithParameter(
                AnalyticsEvent.EVENT_GROUP_DK.eventName,
                AnalyticsEventParam.VEHICLE_DIGITAL_KEY_SHARED_DECLINE
            )
        }
    }
