/*
* Copyright © 2025. Toyota Motors North America Inc
* All rights reserved.
*/

package com.toyota.oneapp.features.dashboard.connectedvehicle.dk.mm21.domain.usecase

import com.toyota.oneapp.features.dashboard.connectedvehicle.dk.mm21.domain.model.DigitalKeyStatus
import com.toyota.oneapp.features.dashboard.connectedvehicle.dk.mm21.domain.model.SharedKeyRevocationRequest
import com.toyota.oneapp.features.dashboard.connectedvehicle.dk.mm21.domain.repository.DigitalKeyGen1Repository
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import javax.inject.Inject

/**
 * Use case for revoking shared digital keys
 */
class RevokeSharedKeyUseCase @Inject constructor(
    private val repository: DigitalKeyGen1Repository
) {
    
    /**
     * Revokes a shared digital key
     * @param request The shared key revocation request
     * @return Flow of DigitalKeyStatus representing the operation status
     */
    operator fun invoke(request: SharedKeyRevocationRequest): Flow<DigitalKeyStatus> = flow {
        emit(DigitalKeyStatus.Loading)
        
        try {
            val result = repository.revokeSharedKeyFromCTP(request)
            
            result.fold(
                onSuccess = {
                    // Remove the invite from local storage
                    repository.removeLocalInvite(request.phoneNumber)
                    emit(DigitalKeyStatus.Success)
                },
                onFailure = { exception ->
                    emit(DigitalKeyStatus.Error(exception.message ?: "Failed to revoke shared key"))
                }
            )
        } catch (e: Exception) {
            emit(DigitalKeyStatus.Error(e.message ?: "Unknown error occurred"))
        }
    }
}
