/*
* Copyright © 2025. Toyota Motors North America Inc
* All rights reserved.
*/

package com.toyota.oneapp.features.dashboard.connectedvehicle.dk.mm21.presentation.manage

import android.app.Activity
import android.content.Intent
import androidx.lifecycle.viewModelScope
import com.toyota.oneapp.digitalkey.onboarding.DkOnBoardingActivity
import com.toyota.oneapp.features.core.presentation.BaseViewModel
import com.toyota.oneapp.features.dashboard.connectedvehicle.dk.domain.model.LuksStatusInvite
import com.toyota.oneapp.features.dashboard.connectedvehicle.dk.mm21.application.ManageDigitalKeyGen1UseCase
import com.toyota.oneapp.features.dashboard.connectedvehicle.dk.mm21.presentation.state.DigitalKeyState
import com.toyota.oneapp.features.dashboard.connectedvehicle.dk.mm21.presentation.state.ManageDigitalKeyEvent
import com.toyota.oneapp.model.vehicle.VehicleInfo
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class ManageDigitalKeyGen1ViewModel
    @Inject
    constructor(
        private val manageDigitalKeyGen1UseCase: ManageDigitalKeyGen1UseCase,
    ) : BaseViewModel<ManageDigitalKeyGen1State, ManageDigitalKeyEvent>() {
        init {
            onEvent(ManageDigitalKeyEvent.LoadOwnerInfo)
        }

        override fun defaultState(): ManageDigitalKeyGen1State = ManageDigitalKeyGen1State.Loading

        override fun onEvent(event: ManageDigitalKeyEvent) {
            when (event) {
                is ManageDigitalKeyEvent.LoadOwnerInfo -> {
                    loadOwnerInfo()
                }
                is ManageDigitalKeyEvent.RefreshData -> {
                    loadOwnerInfo()
                }
                is ManageDigitalKeyEvent.RemoveOwnerKey -> {
                    removeOwnerKey()
                }
                is ManageDigitalKeyEvent.RevokeSharedKey -> {
                    revokeSharedKey(event.invite)
                }
                is ManageDigitalKeyEvent.SyncKeys -> {
                    syncKeys()
                }
                is ManageDigitalKeyEvent.ShowError -> {
                    state.update { currentState ->
                        ManageDigitalKeyGen1State.Error(
                            message = event.message,
                            errorType = event.errorType,
                            canRetry = true
                        )
                    }
                }
                is ManageDigitalKeyEvent.ClearError -> {
                    onEvent(ManageDigitalKeyEvent.LoadOwnerInfo)
                }
                is ManageDigitalKeyEvent.Retry -> {
                    onEvent(ManageDigitalKeyEvent.LoadOwnerInfo)
                }
                is ManageDigitalKeyEvent.LogAnalyticsEvent -> {
                    manageDigitalKeyGen1UseCase.logAnalyticsEvent(event.event, event.param)
                }
            }
        }

        private fun loadOwnerInfo() {
            viewModelScope.launch {
                state.update { ManageDigitalKeyGen1State.Loading }

                // Log screen view
                manageDigitalKeyGen1UseCase.logAnalyticsEvent(
                    "DK_EVENT_GROUP",
                    "DK_MANAGE_SCREEN_VIEW"
                )

                // Load owner info
                manageDigitalKeyGen1UseCase.getOwnerInfo().collect { ownerState ->
                    when (ownerState) {
                        is DigitalKeyState.Success -> {
                            val ownerInfo = ownerState.data

                            // Load shared keys
                            manageDigitalKeyGen1UseCase.getSharedKeys().collect { sharedKeysState ->
                                when (sharedKeysState) {
                                    is DigitalKeyState.Success -> {
                                        val sharedKeys = sharedKeysState.data
                                        val luksStatusInvites = sharedKeys.map { it.toLuksStatusInvite() }

                                        state.update {
                                            ManageDigitalKeyGen1State.Success(
                                                vehicle = null,
                                                ownerName = "${ownerInfo.firstName} ${ownerInfo.lastName}".trim(),
                                                sharedKeys = luksStatusInvites,
                                                isEligibleForSharing = ownerInfo.isOwner,
                                                canInviteUsers = ownerInfo.isOwner,
                                                lastSyncTime = System.currentTimeMillis()
                                            )
                                        }
                                    }
                                    is DigitalKeyState.Error -> {
                                        state.update {
                                            ManageDigitalKeyGen1State.Error(
                                                message = sharedKeysState.message ?: "Failed to load shared keys",
                                                errorType = ErrorType.GENERAL,
                                                canRetry = true
                                            )
                                        }
                                    }
                                    else -> {
                                        // Loading state, continue waiting
                                    }
                                }
                            }
                        }
                        is DigitalKeyState.Error -> {
                            val errorType = when {
                                ownerState.message?.contains("network", ignoreCase = true) == true -> ErrorType.NETWORK
                                ownerState.message?.contains("permission", ignoreCase = true) == true -> ErrorType.PERMISSION_DENIED
                                ownerState.message?.contains("auth", ignoreCase = true) == true -> ErrorType.AUTHENTICATION
                                else -> ErrorType.GENERAL
                            }

                            state.update {
                                ManageDigitalKeyGen1State.Error(
                                    message = ownerState.message ?: "Failed to load digital key information",
                                    errorType = errorType,
                                    canRetry = errorType != ErrorType.PERMISSION_DENIED
                                )
                            }
                        }
                        else -> {
                            // Loading state, continue waiting
                        }
                    }
                }
            }
        }

        private fun syncKeys() {
            viewModelScope.launch {
                val currentState = state.value
                if (currentState is ManageDigitalKeyGen1State.Success) {
                    state.update { currentState.copy(isSyncing = true) }

                    manageDigitalKeyGen1UseCase.syncKeys().collect { syncState ->
                        when (syncState) {
                            is DigitalKeyState.Success -> {
                                state.update {
                                    currentState.copy(
                                        isSyncing = false,
                                        lastSyncTime = System.currentTimeMillis()
                                    )
                                }
                                // Refresh data after sync
                                onEvent(ManageDigitalKeyEvent.LoadOwnerInfo)
                            }
                            is DigitalKeyState.Error -> {
                                state.update {
                                    ManageDigitalKeyGen1State.Error(
                                        message = syncState.message ?: "Sync failed",
                                        errorType = ErrorType.SYNC_FAILED,
                                        canRetry = true
                                    )
                                }
                            }
                            else -> {
                                // Loading state, continue waiting
                            }
                        }
                    }
                }
            }
        }

        fun onSetupClick() {
            onEvent(ManageDigitalKeyEvent.SyncKeys)
        }

        private fun removeOwnerKey() {
            viewModelScope.launch {
                val currentState = state.value
                if (currentState is ManageDigitalKeyGen1State.Success) {
                    state.update { currentState.copy(isDeleting = true) }

                    manageDigitalKeyGen1UseCase.deleteOwnerKey().collect { deleteState ->
                        when (deleteState) {
                            is DigitalKeyState.Success -> {
                                // Key deleted successfully, refresh data
                                onEvent(ManageDigitalKeyEvent.LoadOwnerInfo)
                            }
                            is DigitalKeyState.Error -> {
                                state.update {
                                    ManageDigitalKeyGen1State.Error(
                                        message = deleteState.message ?: "Failed to delete digital key",
                                        errorType = ErrorType.KEY_DELETION_FAILED,
                                        canRetry = true
                                    )
                                }
                            }
                            else -> {
                                // Loading state, continue waiting
                            }
                        }
                    }
                }
            }
        }

        fun onRemoveKeyClick() {
            onEvent(ManageDigitalKeyEvent.RemoveOwnerKey)
        }

        fun onHowToUseClick(activity: Activity) {
            // Log how to use click
            analyticsLogger.logEventWithParameter(
                AnalyticsEvent.EVENT_GROUP_DK.eventName,
                AnalyticsEventParam.DK_HOW_TO_USE_CLICKED
            )

            DigitalMopKeyUtils.appendLog(
                "How to use digital key clicked",
                DigitalMopKeyUtils.TAG,
                isDataDogRequired = true,
                isError = false
            )

            activity.startActivity(Intent(activity, DkOnBoardingActivity::class.java))
        }

        private fun revokeSharedKey(invite: LuksStatusInvite) {
            viewModelScope.launch {
                val currentState = state.value
                if (currentState is ManageDigitalKeyGen1State.Success) {
                    state.update { currentState.copy(isDeleting = true) }

                    manageDigitalKeyGen1UseCase.revokeSharedKey(invite).collect { revokeState ->
                        when (revokeState) {
                            is DigitalKeyState.Success -> {
                                // Shared key revoked successfully, refresh data
                                onEvent(ManageDigitalKeyEvent.LoadOwnerInfo)
                            }
                            is DigitalKeyState.Error -> {
                                state.update {
                                    ManageDigitalKeyGen1State.Error(
                                        message = revokeState.message ?: "Failed to revoke shared key",
                                        errorType = ErrorType.KEY_DELETION_FAILED,
                                        canRetry = true
                                    )
                                }
                            }
                            else -> {
                                // Loading state, continue waiting
                            }
                        }
                    }
                }
            }
        }

        /**
         * Refreshes the digital key data
         */
        fun refreshData() {
            onEvent(ManageDigitalKeyEvent.RefreshData)
        }

        /**
         * Revokes a shared key for a specific user
         */
        fun revokeSharedKey(invite: LuksStatusInvite) {
            onEvent(ManageDigitalKeyEvent.RevokeSharedKey(invite))
        }

        /**
         * Retries the last failed operation
         */
        fun retry() {
            onEvent(ManageDigitalKeyEvent.Retry)
        }

        /**
         * Clears the current error state
         */
        fun clearError() {
            onEvent(ManageDigitalKeyEvent.ClearError)
        }

        /**
         * Logs an event with parameter for analytics
         */
        fun logEventWithParam(event: String, param: String) {
            onEvent(ManageDigitalKeyEvent.LogAnalyticsEvent(event, param))
        }
    }
