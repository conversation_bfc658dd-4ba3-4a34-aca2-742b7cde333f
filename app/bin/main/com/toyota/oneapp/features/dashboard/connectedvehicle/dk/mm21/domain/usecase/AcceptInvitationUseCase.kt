/*
* Copyright © 2025. Toyota Motors North America Inc
* All rights reserved.
*/

package com.toyota.oneapp.features.dashboard.connectedvehicle.dk.mm21.domain.usecase

import com.toyota.oneapp.features.dashboard.connectedvehicle.dk.mm21.domain.model.InvitationProcessResult
import com.toyota.oneapp.features.dashboard.connectedvehicle.dk.mm21.domain.model.PendingInviteTask
import com.toyota.oneapp.features.dashboard.connectedvehicle.dk.mm21.domain.repository.DigitalKeyGen1Repository
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import javax.inject.Inject

/**
 * Use case for accepting a digital key invitation
 */
class AcceptInvitationUseCase @Inject constructor(
    private val repository: DigitalKeyGen1Repository
) {
    
    /**
     * Accepts a pending digital key invitation
     * @param inviteTask The pending invitation task to accept
     * @return Flow of InvitationProcessResult
     */
    operator fun invoke(inviteTask: PendingInviteTask): Flow<InvitationProcessResult> = flow {
        try {
            // Get vehicle information for the invitation
            val vehicleInfo = repository.getVehicleInfo(inviteTask.vin)
            
            if (vehicleInfo != null) {
                // Mark vehicle as having digital key
                repository.setVehicleDigitalKeyStatus(inviteTask.vin, true)
                
                // Update application data with the vehicle
                repository.updateVehicleInList(vehicleInfo)
                
                // Set the vehicle as selected if it's not a remote-only user
                val isRemoteOnlyUser = repository.isRemoteOnlyUser()
                if (!isRemoteOnlyUser) {
                    repository.setSelectedVehicle(vehicleInfo)
                } else {
                    repository.updateSelectedVehicleDigitalKeyStatus(true)
                }
                
                // Store vehicle info locally
                repository.setLocalVehicleInfo(vehicleInfo)
                
                // Mark secondary DK as accepted
                repository.setSecondaryDkAcceptedStatus(true)
                
                // Clear pending invites
                repository.clearPendingInvites()
                
                // Log analytics event
                repository.logInvitationAccepted()
                
                emit(InvitationProcessResult.Accepted)
            } else {
                emit(InvitationProcessResult.Failed("Vehicle information not found"))
            }
        } catch (e: Exception) {
            emit(InvitationProcessResult.Failed(e.message ?: "Failed to accept invitation"))
        }
    }
}
