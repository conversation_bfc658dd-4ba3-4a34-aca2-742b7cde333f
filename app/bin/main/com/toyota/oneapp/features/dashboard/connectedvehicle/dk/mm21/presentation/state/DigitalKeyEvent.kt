/*
* Copyright © 2025. Toyota Motors North America Inc
* All rights reserved.
*/

package com.toyota.oneapp.features.dashboard.connectedvehicle.dk.mm21.presentation.state

import android.content.Context
import androidx.fragment.app.FragmentActivity
import com.toyota.oneapp.features.core.presentation.UiEvent
import com.toyota.oneapp.features.dashboard.connectedvehicle.advanceremote.domain.model.RemoteCommandType
import com.toyota.oneapp.features.dashboard.connectedvehicle.dk.domain.model.LuksStatusInvite
import com.toyota.oneapp.features.dashboard.connectedvehicle.dk.mm21.presentation.manage.ErrorType

sealed interface DigitalKeyEvent {
    data object RequestBluetoothPermission : DigitalKeyEvent

    data object RequestDkPermission : DigitalKeyEvent

    data object DkMigrationCheck : DigitalKeyEvent

    data class StartEnrollment(
        val context: Context,
    ) : DigitalKeyEvent

    data class ShowPermissionError(
        val message: String,
    ) : DigitalKeyEvent

    data class StartDownload(
        val context: Context,
        val activity: FragmentActivity,
    ) : DigitalKeyEvent

    data object DkMopSyncDuringDownload : DigitalKeyEvent

    data object StopDownload : DigitalKeyEvent

    data class KeyRotationChoice(
        val isRotationKeySelected: Boolean,
    ) : DigitalKeyEvent

    data object LukKeyRequest : DigitalKeyEvent

    data object OwnerKeyRequest : DigitalKeyEvent

    data object DownloadOwnerToken : DigitalKeyEvent

    data class DownloadKeyResult(
        val result: Boolean,
    ) : DigitalKeyEvent

    data object DownloadLukToken : DigitalKeyEvent

    data class LaunchRotation(
        val message: String,
    ) : DigitalKeyEvent

    data object GetKeyStatus : DigitalKeyEvent

    data class KeyStatusDuringDownload(
        var isError: Boolean,
        var requestOwnerKey: Boolean = false,
        val errorMessage: String? = null,
    ) : DigitalKeyEvent

    data class ShowBluetoothMessage(
        val message: String,
    ) : DigitalKeyEvent

    data class NavigateToSetup(
        val isSetup: Boolean,
    ) : DigitalKeyEvent

    data object VerifyPhone : DigitalKeyEvent

    data class PhoneVerificationResult(
        val resultCode: Int,
    ) : DigitalKeyEvent

    data class DkStatusCheckFromAPI(
        val deviceId: String,
    ) : DigitalKeyEvent

    data class DownloadOwnerTokenFromAPI(
        val deviceId: String,
    ) : DigitalKeyEvent

    data class DownloadRotationOrLukFromAPI(
        val deviceId: String,
        val tokenType: String,
    ) : DigitalKeyEvent

    data class DkConnect(
        val remoteCommandType: RemoteCommandType,
    ) : DigitalKeyEvent
}

sealed interface DigitalKeyUIEvent : UiEvent {
    data class ShowDialog(
        val message: String,
    ) : DigitalKeyUIEvent

    data class OpenActivity(
        val isSetup: Boolean,
    ) : DigitalKeyUIEvent

    data object NavigateToPhoneVerification : DigitalKeyUIEvent

    data class ShowToast(
        val message: String,
    ) : DigitalKeyUIEvent
}

// Events specific to the Manage Digital Key screen
sealed interface ManageDigitalKeyEvent {
    data object LoadOwnerInfo : ManageDigitalKeyEvent

    data object RefreshData : ManageDigitalKeyEvent

    data object RemoveOwnerKey : ManageDigitalKeyEvent

    data class RevokeSharedKey(val invite: LuksStatusInvite) : ManageDigitalKeyEvent

    data object SyncKeys : ManageDigitalKeyEvent

    data class ShowError(val message: String, val errorType: ErrorType = ErrorType.GENERAL) : ManageDigitalKeyEvent

    data object ClearError : ManageDigitalKeyEvent

    data object Retry : ManageDigitalKeyEvent

    data class LogAnalyticsEvent(val event: String, val param: String) : ManageDigitalKeyEvent
}
