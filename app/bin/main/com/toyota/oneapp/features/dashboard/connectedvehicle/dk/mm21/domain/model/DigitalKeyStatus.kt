/*
* Copyright © 2025. Toyota Motors North America Inc
* All rights reserved.
*/

package com.toyota.oneapp.features.dashboard.connectedvehicle.dk.mm21.domain.model

/**
 * Represents the status of a digital key operation
 */
sealed class DigitalKeyStatus {
    object Loading : DigitalKeyStatus()
    object Success : DigitalKeyStatus()
    data class Error(val message: String?, val code: Int? = null) : DigitalKeyStatus()
}

/**
 * Represents the status of key synchronization
 */
sealed class SyncStatus {
    object Idle : SyncStatus()
    object InProgress : SyncStatus()
    object Completed : SyncStatus()
    data class Failed(val error: String) : SyncStatus()
}

/**
 * Represents the result of a key revocation operation
 */
sealed class KeyRevocationResult {
    object Success : KeyRevocationResult()
    data class Failed(val error: String, val code: Int? = null) : KeyRevocationResult()
}

/**
 * Represents different types of key revocation
 */
enum class KeyRevocationType(val type: String) {
    PRIMARY("PRIMARY"),
    LUK_SELF("LUK_SELF"),
    PRIMARY_LUK("PRIMARY_LUK")
}

/**
 * Represents the status of a shared key invitation
 */
enum class InvitationStatus(val status: String) {
    PENDING("PENDING"),
    ACTIVE("ACTIVE"),
    SMS_SENT("SMS_SENT"),
    DECLINED("DECLINED"),
    EXPIRED("EXPIRED")
}
