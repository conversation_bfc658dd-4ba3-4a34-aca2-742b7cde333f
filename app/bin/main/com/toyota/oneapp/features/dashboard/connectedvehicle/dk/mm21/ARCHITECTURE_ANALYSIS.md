# Digital Key Gen1 Architecture Migration Analysis

## Current State Assessment

### What's Already Implemented (Clean Architecture)
1. **Domain Models**:
   - `DigitalKeyOwner` - Basic owner information
   - `SharedDigitalKey` - Shared key information
   - `CalibrationSetting` - Calibration configuration
   - `ContactInfo` - Contact details
   - `DigitalKeyInviteRequest` - Invitation request model

2. **Use Cases**:
   - `GetDigitalKeyOwnerUseCase` - Retrieves owner information
   - `GetSharedKeysUseCase` - Retrieves shared keys list
   - `GetContactDetailsUseCase` - Contact picker functionality
   - `InviteDigitalKeyUserUseCase` - User invitation
   - `GetCalibrationSettingUseCase` - Calibration settings
   - `SetCalibrationLevelUseCase` - Calibration adjustment
   - `ToggleManualSettingUseCase` - Manual calibration toggle
   - `IsBluetoothEnabledUseCase` - Bluetooth status check

3. **Repository**:
   - `DigitalKeyGen1Repository` interface with basic operations
   - `DigitalKeyGen1RepositoryImpl` with partial implementation

4. **Presentation**:
   - `ManageDigitalKeyGen1ViewModel` with mock data
   - `ManageDigitalKeyGen1Screen` with complete UI
   - `ManageDigitalKeyGen1State` for state management

### What's Missing (From Legacy DigitalKeyManageViewModel)

#### Critical Business Logic Missing:
1. **Key Deletion & Revocation**:
   - `removeOwnerKeyFromCTP()` - Owner key removal
   - `removeUserFromCTP()` - Shared key revocation
   - Complex revocation logic for different key types (PRIMARY, LUK_SELF, PRIMARY_LUK)
   - `deactivateKey()` - Local key deactivation
   - `handleRevokeSuccess()` / `handleRevokeFailure()` - Revocation result handling

2. **Shared Key Management**:
   - `getSharedKeysFromCTP()` - API-based shared keys retrieval
   - `digitalKeyInvite()` - Complete invitation flow
   - `completePersonaEvent()` - Invitation acceptance/decline
   - `showPendingInviteTasks()` - Pending invitation handling
   - `declineSharedKeyInvitation()` - Invitation decline logic

3. **Synchronization & Status**:
   - `syncDigitalData()` - Key synchronization
   - `populateInviteData()` - Local invite data population
   - Device ID management and retrieval
   - Key status monitoring and updates

4. **Calibration Management**:
   - `setBleCalibration()` - BLE calibration setting
   - `getCalibrationStatus()` - Calibration status retrieval
   - `setCalibrationSetting()` - Calibration configuration
   - `setDkLibBleFunctionOFF()` - Bluetooth reconnection for settings

5. **Error Handling & Analytics**:
   - Comprehensive error handling with specific error codes
   - Analytics logging for all key operations
   - DataDog logging integration
   - Progress state management

#### Missing Domain Models:
1. **DkCalibrationStatus** - Calibration status with manual setting flag
2. **DigitalKeyStatus** - Key status enumeration
3. **KeyRevocationResult** - Revocation operation result
4. **SyncStatus** - Synchronization status
5. **PendingInviteTask** - Pending invitation task model

#### Missing Use Cases:
1. **DeleteDigitalKeyUseCase** - Complete key deletion
2. **RevokeSharedKeyUseCase** - Shared key revocation
3. **SyncDigitalKeysUseCase** - Key synchronization
4. **GetKeyStatusUseCase** - Key status retrieval
5. **AcceptInvitationUseCase** - Invitation acceptance
6. **DeclineInvitationUseCase** - Invitation decline
7. **GetPendingInvitationsUseCase** - Pending invitations
8. **UpdateCalibrationUseCase** - Calibration updates

#### Missing Repository Methods:
1. Key deletion and revocation operations
2. Shared key management operations
3. Synchronization operations
4. Status monitoring operations
5. Analytics and logging operations

## Migration Strategy

### Phase 1: Core Domain Models
Create missing domain models that represent the business entities properly.

### Phase 2: Repository Enhancement
Extend the repository interface and implementation with missing operations.

### Phase 3: Use Cases Implementation
Implement the missing use cases that encapsulate business logic.

### Phase 4: ViewModel Migration
Replace mock data and TODO implementations with real business logic.

### Phase 5: Error Handling & Analytics
Add comprehensive error handling and analytics integration.

### Phase 6: Testing & Validation
Create unit tests and validate the migration.

## Key Dependencies to Maintain
- `DigitalMopKeyUtils` - Core digital key operations
- `DigitalKeyLocalData` - Local data management
- `DigitalKeyDownloadLogic` - Download and API operations
- `ApplicationData` - Vehicle and user data
- Analytics and logging frameworks

## Risk Assessment
- **High**: Key deletion and revocation logic is complex and critical
- **Medium**: Shared key management has multiple edge cases
- **Low**: Calibration and status operations are straightforward

## Success Criteria
1. ✅ All existing functionality from DigitalKeyManageViewModel is available
2. ✅ Clean architecture principles are maintained
3. ✅ UI remains unchanged but uses real data
4. ✅ All error scenarios are properly handled
5. ✅ Analytics and logging continue to work
6. ✅ Unit tests cover critical business logic

## Migration Completed

### What Was Accomplished
1. **Domain Models Created**: Added missing domain models for DigitalKeyStatus, CalibrationStatus, PendingInviteTask, and DigitalKeyOperation
2. **Use Cases Implemented**: Created comprehensive use cases for all key operations including DeleteDigitalKeyUseCase, SyncDigitalKeysUseCase, RevokeSharedKeyUseCase, and others
3. **Repository Enhanced**: Extended DigitalKeyGen1Repository with all necessary methods and implemented them in DigitalKeyGen1RepositoryImpl
4. **ViewModel Updated**: Replaced mock data in ManageDigitalKeyGen1ViewModel with real business logic using the new use cases
5. **Error Handling**: Added comprehensive error handling with specific error types and retry mechanisms
6. **Analytics Integration**: Integrated AnalyticsLogger and DataDog logging throughout the application
7. **State Management**: Enhanced state management with loading, success, and error states
8. **Unit Tests**: Created comprehensive unit tests for use cases, ViewModels, and integration scenarios

### Key Features Migrated
- ✅ Digital key owner information retrieval
- ✅ Shared keys management and display
- ✅ Key deletion and revocation (PRIMARY, LUK_SELF, PRIMARY_LUK)
- ✅ Digital key synchronization
- ✅ Error handling with retry mechanisms
- ✅ Analytics logging for all operations
- ✅ DataDog logging for debugging and monitoring
- ✅ Calibration management
- ✅ Invitation management (accept/decline)

### Architecture Benefits Achieved
- **Separation of Concerns**: Business logic is now properly separated into domain, data, and presentation layers
- **Testability**: All components are easily testable with dependency injection
- **Maintainability**: Code is more organized and easier to maintain
- **Scalability**: New features can be easily added following the established patterns
- **Error Handling**: Comprehensive error handling with specific error types
- **Analytics**: Consistent analytics and logging throughout the application
