package com.toyota.oneapp.features.privacyportal.presentation.widgets

import androidx.compose.material.ModalBottomSheetState
import com.toyota.oneapp.features.privacyportal.domain.model.DetailItemModel
import com.toyota.oneapp.features.privacyportal.presentation.vehiclespecific.additionalfeatures.ManageConsentUiState
import kotlinx.coroutines.CoroutineScope

data class AdditionalFeaturesParams(
    val isSms: Boolean,
    val isKDDI: Boolean,
    val showManage: <PERSON>olean,
    val consentName: String,
    val acceptedDateText: String,
    val shortDesc: String,
    val shortDescHtml: String?,
    val imageUrl: String?,
    val dataSharedItems: List<DetailItemModel>,
    val howToTurnOffDesc: String?,
    val sheetState: ModalBottomSheetState,
    val manageConsentUiState: ManageConsentUiState,
    val scope: CoroutineScope,
)
