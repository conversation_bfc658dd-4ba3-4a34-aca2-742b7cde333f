/*
 * Copyright © 2025. Toyota Motors North America Inc
 * All rights reserved.
 */

package com.toyota.oneapp.features.privacyportal.presentation.widgets

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import com.toyota.oneapp.R
import com.toyota.oneapp.features.core.composable.OABody2TextView
import com.toyota.oneapp.features.core.composable.OABody3TextView
import com.toyota.oneapp.features.core.theme.AppTheme

@Composable
fun WifiConnectActiveCard() {
    Column(
        modifier =
            Modifier
                .fillMaxWidth()
                .background(AppTheme.colors.primaryLightBlue)
                .padding(16.dp),
    ) {
        OABody2TextView(
            text = stringResource(R.string.wifi_connect_is_active),
            color = AppTheme.colors.textPrimary,
        )
        Spacer(modifier = Modifier.height(8.dp))
        OABody3TextView(
            text = stringResource(R.string.wifi_connect_cancel_instructions),
            color = AppTheme.colors.textPrimary,
        )
    }
}
