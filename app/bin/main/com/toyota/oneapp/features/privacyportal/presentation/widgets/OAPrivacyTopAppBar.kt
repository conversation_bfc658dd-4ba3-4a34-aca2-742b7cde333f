/*
 * Copyright © 2025. Toyota Motors North America Inc
 * All rights reserved.
 *
 */

package com.toyota.oneapp.features.privacyportal.presentation.widgets

import androidx.annotation.DrawableRes
import androidx.compose.foundation.Image
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material.Divider
import androidx.compose.material.Surface
import androidx.compose.material.TopAppBar
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.ColorFilter
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.toyota.oneapp.R
import com.toyota.oneapp.features.core.composable.OABody1TextView
import com.toyota.oneapp.features.core.composable.OABody3TextView
import com.toyota.oneapp.features.core.theme.AppTheme

@Composable
fun OAPrivacyTopAppBar(
    modifier: Modifier = Modifier,
    title: String,
    showBackButton: Boolean = false,
    onBack: (() -> Unit)? = null,
    @DrawableRes backIcon: Int = R.drawable.ic_back_arrow,
    showCancelButton: Boolean = false,
    onCancel: (() -> Unit)? = null,
) {
    Column(modifier = modifier) {
        TopAppBar(
            modifier = Modifier.height(64.dp),
            backgroundColor = AppTheme.colors.tertiary15,
            elevation = 0.dp,
            navigationIcon = {
                if (showBackButton && onBack != null) {
                    Surface(
                        shape = CircleShape,
                        color = AppTheme.colors.button02d,
                        modifier =
                            Modifier
                                .padding(start = 8.dp)
                                .size(48.dp),
                    ) {
                        Image(
                            modifier =
                                Modifier
                                    .clickable { onBack() }
                                    .padding(
                                        start = 19.dp,
                                        end = 22.dp,
                                        top = 17.dp,
                                        bottom = 17.dp,
                                    ),
                            painter = painterResource(id = backIcon),
                            colorFilter = ColorFilter.tint(color = AppTheme.colors.tertiary03),
                            contentDescription = stringResource(id = R.string.Common_back),
                        )
                    }
                }
            },
            title = {
                Box(
                    modifier =
                        Modifier
                            .fillMaxWidth()
                            .padding(end = 50.dp),
                    contentAlignment = Alignment.Center,
                ) {
                    OABody3TextView(
                        text = title,
                        color = AppTheme.colors.tertiary03,
                    )
                }
            },
            actions = {
                if (showCancelButton && onCancel != null) {
                    OABody1TextView(
                        text = stringResource(R.string.dialog_cancel),
                        color = AppTheme.colors.error01,
                        modifier =
                            Modifier
                                .padding(horizontal = 8.dp)
                                .clickable { onCancel() },
                    )
                }
            },
        )

        Divider(
            color = AppTheme.colors.tertiary10,
            thickness = 1.dp,
        )
    }
}

@Preview
@Composable
fun OAPrivacyTopAppBarPreview() {
    OAPrivacyTopAppBar(
        title = "Privacy Screen",
        showBackButton = false,
        onBack = null,
        showCancelButton = true,
        onCancel = {},
    )
}
