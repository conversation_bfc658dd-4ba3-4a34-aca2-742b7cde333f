/*
 * Copyright © 2024. Toyota Motors North America IncAll rights reserved.
 */

package com.toyota.oneapp.features.publiccharging.domain.model

import com.google.gson.annotations.SerializedName

data class StopChargingData(
    val messages: Messages,
    val payload: StopChargingPayload,
)

data class StopChargingPayload(
    val session: Session,
)

data class Session(
    @SerializedName("_id") val id: String,
    @SerializedName("charging_id") val chargingId: String,
    @SerializedName("created_at") val createdAt: String,
    @SerializedName("data") val `data`: Data,
    @SerializedName("status_code") val statusCode: Int,
    @SerializedName("timestamp") val timestamp: String,
    @SerializedName("updated_at") val updatedAt: String,
)

data class Data(
    @SerializedName("auth_method") val authMethod: String,
    val currency: String,
    val id: String,
    val kwh: Int,
    val lastMeterReadOn: String,
    @SerializedName("last_updated") val lastUpdated: String,
    val location: Location,
    val power: Int,
    val startOn: String,
    @SerializedName("start_datetime") val startDatetime: String,
    val status: String,
    val stopOn: String,
    @SerializedName("total_cost") val totalCost: Int,
)

data class Location(
    val address: String,
    val city: String,
    val coordinates: ChargingStationCoordinates,
    val country: String,
    val directions: List<Direction>,
    val evses: List<Evse>,
    val id: String,
    @SerializedName("last_updated") val lastUpdated: String,
    @SerializedName("mbx_ext") val mbxExt: MbxExt,
    val name: String,
    @SerializedName("opening_times") val openingTimes: OpeningTimes,
    @SerializedName("operator") val `operator`: Operator,
    @SerializedName("postal_code") val postalCode: String,
    val province: String,
    @SerializedName("time_zone") val timeZone: String,
    val type: String,
)

data class ChargingStationCoordinates(
    val latitude: String,
    val longitude: String,
)

data class Direction(
    val language: String,
    val text: String,
)

data class Evse(
    val capabilities: List<String>,
    val connectors: List<Connector>,
    val coordinates: ChargingStationCoordinates,
    @SerializedName("evse_id") val evseId: String,
    @SerializedName("floor_level") val floorLevel: String,
    @SerializedName("last_updated") val lastUpdated: String,
    @SerializedName("parking_restrictions") val parkingRestrictions: List<String>,
    @SerializedName("physical_reference") val physicalReference: String,
    val status: String,
    val uid: String,
)

data class Connector(
    val amperage: Int,
    val format: String,
    val id: String,
    @SerializedName("last_updated") val lastUpdated: String,
    @SerializedName("max_power") val maxPower: Int,
    @SerializedName("power_type") val powerType: String,
    val standard: String,
    @SerializedName("tariff_id") val tariffId: String,
    val voltage: Int,
)

data class ExceptionalClosing(
    @SerializedName("period_begin") val periodBegin: String,
    @SerializedName("period_end") val periodEnd: String,
)

data class ExceptionalOpening(
    @SerializedName("period_begin") val periodBegin: String,
    @SerializedName("period_end") val periodEnd: String,
)

data class Logo(
    val category: String,
    val height: Int,
    val thumbnail: String,
    val type: String,
    val url: String,
    val width: Int,
)

data class MbxExt(
    val amenities: List<Amenity>,
    @SerializedName("partner_type") val partnerType: String,
    @SerializedName("pnc_capable") val pncCapable: Boolean,
)

data class OpeningTimes(
    val exceptionalClosings: List<ExceptionalClosing>,
    val exceptionalOpenings: List<ExceptionalOpening>,
    @SerializedName("regular_hour") val regularHour: List<RegularHour>,
    val timing: String,
    @SerializedName("twentyfourseven") val twentyfourSeven: Boolean,
)

data class Operator(
    val logo: Logo,
    val name: String,
    val website: String,
)

data class RegularHour(
    @SerializedName("period_begin") val periodBegin: String,
    @SerializedName("period_end") val periodEnd: String,
    val weekday: String,
)

data class Amenity(
    val type: String,
)
