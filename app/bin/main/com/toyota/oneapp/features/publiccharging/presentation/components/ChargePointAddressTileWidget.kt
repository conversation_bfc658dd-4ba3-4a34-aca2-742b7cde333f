/*
 * Copyright © 2024. Toyota Motors North America IncAll rights reserved.
 */

package com.toyota.oneapp.features.publiccharging.presentation.components

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Card
import androidx.compose.material.ExperimentalMaterialApi
import androidx.compose.material.Surface
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.ColorFilter
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.toyota.oneapp.R
import com.toyota.oneapp.features.core.composable.OABody4TextView
import com.toyota.oneapp.features.core.composable.OACallOut1TextView
import com.toyota.oneapp.features.core.theme.AppTheme
import com.toyota.oneapp.util.ToyotaConstants

@OptIn(ExperimentalMaterialApi::class)
@Composable
fun ChargePointAddressTileWidget(
    address: ChargePointAddress,
    modifier: Modifier = Modifier,
) {
    Card(
        backgroundColor = AppTheme.colors.tile01,
        elevation = 8.dp,
        shape = RoundedCornerShape(8.dp),
        modifier =
            modifier
                .fillMaxWidth()
                .wrapContentHeight()
                .padding(bottom = 8.dp),
        onClick = { },
    ) {
        Row(
            verticalAlignment = Alignment.CenterVertically,
            modifier =
                Modifier.padding(
                    all = 12.dp,
                ),
        ) {
            Box(
                modifier =
                    Modifier
                        .width(48.dp),
            ) {
                Surface(
                    shape = CircleShape,
                    color = AppTheme.colors.button02d,
                    modifier =
                        Modifier
                            .size(size = 48.dp)
                            .align(Alignment.CenterStart),
                ) {
                    Image(
                        modifier =
                            Modifier
                                .padding(
                                    all = 12.dp,
                                ),
                        painter = painterResource(id = R.drawable.ic_location),
                        colorFilter = ColorFilter.tint(AppTheme.colors.secondary01),
                        contentDescription = null,
                    )
                }
            }
            Spacer(
                modifier =
                    Modifier.width(
                        12.dp,
                    ),
            )

            Column(
                modifier =
                    Modifier
                        .fillMaxWidth()
                        .wrapContentHeight(),
            ) {
                OABody4TextView(
                    text = address.partnerName ?: ToyotaConstants.EMPTY_STRING,
                    color = AppTheme.colors.tertiary03,
                    maxLines = 1,
                )
                Spacer(modifier = Modifier.height(2.dp))
                OACallOut1TextView(
                    text = address.stationName ?: ToyotaConstants.EMPTY_STRING,
                    color = AppTheme.colors.tertiary03,
                    maxLines = 1,
                )
                Spacer(modifier = Modifier.height(2.dp))
                OACallOut1TextView(
                    text = address.stationAddress ?: ToyotaConstants.EMPTY_STRING,
                    color = AppTheme.colors.tertiary05,
                    maxLines = 1,
                )
            }
        }
    }
}

@Preview(showBackground = true)
@Composable
fun ChargePointAddressTileWidgetPreview() {
    ChargePointAddressTileWidget(
        ChargePointAddress("PROMETHEUS STATION", "6565 Headquarters Dr, Plano "),
    )
}
