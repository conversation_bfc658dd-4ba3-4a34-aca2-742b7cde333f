package com.toyota.oneapp.features.publiccharging.dataaccess.service

import com.toyota.oneapp.features.findstations.dataaccess.servermodel.StationsListResponse
import com.toyota.oneapp.features.publiccharging.domain.model.ChargeSessionData
import com.toyota.oneapp.features.publiccharging.domain.model.StartChargingData
import com.toyota.oneapp.features.publiccharging.domain.model.StartChargingRequest
import com.toyota.oneapp.features.publiccharging.domain.model.StopChargingData
import com.toyota.oneapp.features.publiccharging.domain.model.StopChargingRequest
import retrofit2.Response
import retrofit2.http.Body
import retrofit2.http.GET
import retrofit2.http.Header
import retrofit2.http.Path
import retrofit2.http.Query

interface PublicChargingApi {
    @GET("/charging/v2/locations")
    suspend fun fetchAllNearbyStations(
        @Header("x-brand") brand: String,
        @Header("fuelType") fuelType: String,
        @Query("radius") radius: Int,
        @Header("x-region") region: String,
        @Header("x-latitude") latitude: Double?,
        @Header("x-longitude") longitude: Double?,
        @Query("connector") connector: String? = null,
        @Query("displayMode") displayMode: String? = null,
        @Query("partnertype") partnertype: String? = null,
    ): Response<StationsListResponse?>

    @GET("/charger/start")
    suspend fun startCharging(
        @Header("x-country") xCountry: String,
        @Header("x-channel") xChannel: String,
        @Header("x-correlationid") xCorrelationid: String,
        @Header("x-locale") xLocale: String,
        @Header("x-vin") xVin: String,
        @Body startChargingRequest: StartChargingRequest,
    ): Response<StartChargingData?>

    @GET("/charger/stop")
    suspend fun stopCharging(
        @Header("x-make") xMake: String,
        @Header("x-channel") xChannel: String,
        @Header("x-correlationid") xCorrelationid: String,
        @Header("x-locale") xLocale: String,
        @Header("x-vin") xVin: String,
        @Body stopChargingRequest: StopChargingRequest,
    ): Response<StopChargingData?>

    @GET("/charger/session/{id}")
    suspend fun fetchChargeSession(
        @Path("id") id: String,
        @Header("x-make") xMake: String,
        @Header("x-channel") xChannel: String,
        @Header("x-correlationid") xCorrelationid: String,
        @Header("x-locale") xLocale: String,
        @Header("x-vin") xVin: String,
    ): Response<ChargeSessionData?>
}
