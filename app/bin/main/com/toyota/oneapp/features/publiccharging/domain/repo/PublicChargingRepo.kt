package com.toyota.oneapp.features.publiccharging.domain.repo

import com.toyota.oneapp.features.findstations.dataaccess.servermodel.StationsListResponse
import com.toyota.oneapp.features.publiccharging.domain.model.ChargeSessionData
import com.toyota.oneapp.features.publiccharging.domain.model.StartChargingData
import com.toyota.oneapp.features.publiccharging.domain.model.StartChargingRequest
import com.toyota.oneapp.features.publiccharging.domain.model.StopChargingData
import com.toyota.oneapp.features.publiccharging.domain.model.StopChargingRequest
import com.toyota.oneapp.network.Resource

interface PublicChargingRepo {
    suspend fun fetchNearbyStations(
        brand: String,
        fuelType: String,
        radius: Int,
        region: String,
        latitude: Double?,
        longitude: Double?,
    ): Resource<StationsListResponse?>

    suspend fun startCharging(
        make: String,
        country: String,
        channel: String,
        correlationid: String,
        locale: String,
        vin: String,
        startChargingRequest: StartChargingRequest,
    ): Resource<StartChargingData?>

    suspend fun stopCharging(
        make: String,
        channel: String,
        correlationid: String,
        locale: String,
        vin: String,
        stopChargingRequest: StopChargingRequest,
    ): Resource<StopChargingData?>

    suspend fun fetchChargeSession(
        chargingId: String,
        make: String,
        channel: String,
        correlationid: String,
        locale: String,
        vin: String,
    ): Resource<ChargeSessionData?>
}
