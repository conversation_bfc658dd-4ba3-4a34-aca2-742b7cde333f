/*
 * Copyright © 2024. Toyota Motors North America IncAll rights reserved.
 */

package com.toyota.oneapp.features.publiccharging.domain.model

import com.google.gson.annotations.SerializedName

data class StartChargingData(
    val messages: Messages,
    val startChargingPayload: StartChargingPayload,
)

data class Messages(
    val description: String,
    val detailedDescription: String,
    val responseCode: String,
)

data class StartChargingPayload(
    @SerializedName("start_charge_response") val startChargeResponse: StartChargeResponse,
)

data class StartChargeResponse(
    @SerializedName("charging_id") val chargingId: String,
    @SerializedName("created_at") val createdAt: String,
    val status: String,
)
