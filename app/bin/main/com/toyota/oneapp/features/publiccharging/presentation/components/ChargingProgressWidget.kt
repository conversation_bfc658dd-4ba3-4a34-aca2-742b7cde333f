/*
 * Copyright © 2024. Toyota Motors North America IncAll rights reserved.
 */

package com.toyota.oneapp.features.publiccharging.presentation.components

import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.animation.core.tween
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.toyota.oneapp.R
import com.toyota.oneapp.features.core.theme.AppTheme

@Composable
fun ChargingProgress(progressPercentage: Float = 0.0f) {
    val animatedProgress =
        animateFloatAsState(
            targetValue = progressPercentage,
            animationSpec = tween(durationMillis = 500),
            label = stringResource(R.string.charging_animation),
        ).value

    Box(
        modifier =
            Modifier
                .width(170.dp)
                .height(12.dp)
                .clip(RoundedCornerShape(6.dp))
                .background(Color.LightGray),
    ) {
        Box(
            modifier =
                Modifier
                    .fillMaxWidth(animatedProgress)
                    .fillMaxHeight()
                    .clip(RoundedCornerShape(6.dp))
                    .background(AppTheme.colors.secondary01),
        )
    }
}

@Preview(showBackground = true)
@Composable
fun ChargingProgressPreview() {
    ChargingProgress(0.5f)
}
