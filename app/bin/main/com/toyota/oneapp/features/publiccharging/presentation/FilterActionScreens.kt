package com.toyota.oneapp.features.publiccharging.presentation

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material.Chip
import androidx.compose.material.ChipDefaults
import androidx.compose.material.ExperimentalMaterialApi
import androidx.compose.runtime.Composable
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import com.toyota.oneapp.R
import com.toyota.oneapp.features.core.composable.OACallOut2TextView
import com.toyota.oneapp.features.core.theme.AppTheme
import com.toyota.oneapp.features.core.util.LocalBottomSheet
import com.toyota.oneapp.features.core.util.launchSecondaryBottomSheetAction
import kotlinx.coroutines.launch

@Composable
fun FilterScreen(filterMenuData: FilterMenuData) {
    val bottomSheet = LocalBottomSheet.current
    val coroutineScope = rememberCoroutineScope()
    coroutineScope.launchSecondaryBottomSheetAction(
        bottomSheet,
    ) { sheetState ->
        when (filterMenuData.filterType) {
            stringResource(R.string.partners) -> {
                PartnerTypeFilter(filterMenuData) {
                    coroutineScope.launch {
                        sheetState.hide()
                    }
                }
            }
        }
    }
}

@Composable
fun PartnerTypeFilter(
    filterMenuData: FilterMenuData,
    onClosed: () -> Unit,
) {
    val selectedFilters = remember { mutableSetOf<Int>() }
    filterMenuData.filterData.forEach {
        if (it.isSelected) {
            selectedFilters.add(it.filterName)
        }
    }
    Column(
        modifier = Modifier.padding(12.dp),
    ) {
        LazyColumn {
            items(filterMenuData.filterData) { item ->
                FilterView(
                    item,
                    selectedFilters,
                )
            }
        }
        CircleImageView(
            R.drawable.ic_close_24px,
            Modifier.align(
                Alignment.CenterHorizontally,
            ),
        ) {
            onClosed()
        }
    }
}

@OptIn(ExperimentalMaterialApi::class)
@Composable
fun FilterView(
    filterData: FilterData,
    selectedFilters: MutableSet<Int>,
) {
    Spacer(modifier = Modifier.width(10.dp))
    val isSelected = remember { mutableStateOf(selectedFilters.contains(filterData.filterName)) }

    Chip(
        modifier = Modifier.fillMaxWidth(),
        onClick = {
            if (isSelected.value) {
                isSelected.value = false
                selectedFilters.remove(filterData.filterName)
            } else {
                isSelected.value = true
                selectedFilters.add(filterData.filterName)
            }
        },
        colors =
            ChipDefaults.chipColors(
                backgroundColor = AppTheme.colors.button05b,
            ),
    ) {
        Row {
            OACallOut2TextView(
                text = stringResource(filterData.filterName),
                color = AppTheme.colors.tertiary05,
                modifier =
                    Modifier
                        .padding(start = 12.dp)
                        .weight(1f, true),
            )
            if (isSelected.value) {
                Image(
                    painter = painterResource(id = R.drawable.ic_small_tic),
                    contentDescription = stringResource(id = R.string.right_chevron),
                    modifier =
                        Modifier
                            .padding(end = 4.dp)
                            .align(Alignment.CenterVertically)
                            .size(24.dp),
                )
            }
        }
    }
}
