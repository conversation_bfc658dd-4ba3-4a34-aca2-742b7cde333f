package com.toyota.oneapp.features.publiccharging.presentation

import androidx.lifecycle.viewModelScope
import com.google.android.gms.maps.model.LatLng
import com.toyota.oneapp.app.ApplicationData
import com.toyota.oneapp.features.chargeinfo.entrollment.application.EnrollmentState
import com.toyota.oneapp.features.chargeinfo.entrollment.domain.model.EnrollmentData
import com.toyota.oneapp.features.chargeinfo.entrollment.util.Constance
import com.toyota.oneapp.features.findstations.application.FindStationsUseCase
import com.toyota.oneapp.features.findstations.domain.model.SendToCarState
import com.toyota.oneapp.features.publiccharging.application.PublicChargingState
import com.toyota.oneapp.features.publiccharging.application.PublicChargingUseCase
import com.toyota.oneapp.features.publiccharging.domain.model.ChargeStationInfo
import com.toyota.oneapp.model.vehicle.VehicleInfo
import com.toyota.oneapp.sharedpref.OneAppPreferenceModel
import com.toyota.oneapp.ui.BaseViewModel
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class PublicChargingViewModel
    @Inject
    constructor(
        private val publicChargingUseCase: PublicChargingUseCase,
        private val findStationsUseCase: FindStationsUseCase,
        private val preferenceModel: OneAppPreferenceModel,
        applicationData: ApplicationData,
    ) : BaseViewModel() {
        private val _stationState =
            MutableStateFlow<PublicChargingState>(value = PublicChargingState.Init)
        val stationState = _stationState.asStateFlow()
        var vehicleInfo: VehicleInfo? = applicationData.getSelectedVehicle()
        private val _sendToCarState = MutableStateFlow<SendToCarState>(SendToCarState.Init)
        val sendToCarState = _sendToCarState.asStateFlow()

        fun fetchStations(positionInfo: LatLng?) {
            vehicleInfo?.let { vehicleInfo ->
                _stationState.value = PublicChargingState.Loading
                viewModelScope.launch {
                    publicChargingUseCase.fetchNearByStations(
                        positionInfo?.latitude,
                        positionInfo?.longitude,
                        vehicleInfo,
                    ).collect {
                        if (it.isEmpty()) {
                            _stationState.value = PublicChargingState.EmptyStations
                        } else {
                            _stationState.value = PublicChargingState.Success(it, positionInfo)
                        }
                    }
                }
            }
        }

        fun mapStationButtonTitle(
            station: ChargeStationInfo?,
            enrollmentState: StateFlow<EnrollmentState>,
        ): String? {
            var title: String? = Constance.SETUP_WALLET
            if (station?.evIsPartner == true) return title
            if (enrollmentState.value !is EnrollmentState.Success) return title
            val enrollmentStateData = (enrollmentState.value as EnrollmentState.Success).data
            title =
                when (station?.evEvSource) {
                    Constance.CHARGE_POINT -> buttonTitle(enrollmentStateData)
                    Constance.EV_GO -> buttonTitle(enrollmentStateData)
                    else -> title
                }
            return title
        }

        private fun buttonTitle(enrollmentData: EnrollmentData): String {
            return if (!enrollmentData.chargePointStatus) {
                Constance.UNLOCK_STATION
            } else if (!enrollmentData.evGoPointStatus) {
                Constance.START_CHARGING
            } else {
                Constance.REGISTER
            }
        }

        fun sendPOIToCar(stationInfo: ChargeStationInfo) {
            viewModelScope.launch {
                vehicleInfo?.let { vehicle ->
                    _sendToCarState.value = SendToCarState.Loading
                    findStationsUseCase.sendPOIToCar(
                        text = getPOIRequestText(stationInfo),
                        guid = preferenceModel.getGuid(),
                        generation = vehicle.generation,
                        region = vehicle.region,
                        vin = vehicle.vin,
                    )
                        .collect { status ->
                            _sendToCarState.value = status
                        }
                }
            }
        }

        private fun getPOIRequestText(station: ChargeStationInfo): String {
            return with(station) {
                "$stationName, $addressLine1, $addressLine2"
            }
        }

        fun hideSendToCarSuccess() {
            _sendToCarState.value = SendToCarState.Init
        }
    }
