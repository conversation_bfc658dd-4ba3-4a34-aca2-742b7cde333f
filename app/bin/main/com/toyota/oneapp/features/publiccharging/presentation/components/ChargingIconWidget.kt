/*
 * Copyright © 2024. Toyota Motors North America IncAll rights reserved.
 */

package com.toyota.oneapp.features.publiccharging.presentation.components

import androidx.compose.foundation.layout.size
import androidx.compose.material.Icon
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.unit.dp
import com.toyota.oneapp.R
import com.toyota.oneapp.features.core.theme.AppTheme
import com.toyota.oneapp.features.core.util.ContentDescriptions

@Composable
fun ChargingIcon(isBatteryLow: Boolean) {
    val tintColor =
        if (isBatteryLow) {
            AppTheme.colors.primary01
        } else {
            AppTheme.colors.secondary01
        }

    Icon(
        painter = painterResource(R.drawable.charge_info),
        modifier = Modifier.size(48.dp),
        tint = tintColor,
        contentDescription = ContentDescriptions.Charge_Flash_Icon,
    )
}
