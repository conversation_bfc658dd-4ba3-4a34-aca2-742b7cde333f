/*
 * Copyright © 2024. Toyota Motors North America Inc
 * All rights reserved.
 */

package com.toyota.oneapp.features.publiccharging.presentation.components

import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material.Icon
import androidx.compose.material.Surface
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.unit.dp
import com.toyota.oneapp.R
import com.toyota.oneapp.features.core.composable.testTagID
import com.toyota.oneapp.features.core.theme.AppTheme
import com.toyota.oneapp.features.core.util.AccessibilityId

@Composable
fun RefreshIconWidget() {
    Surface(
        shape = CircleShape,
        color = AppTheme.colors.button02d,
        modifier =
            Modifier
                .size(64.dp)
                .padding(8.dp)
                .clickable { }
                .testTagID(AccessibilityId.ID_VEHICLE_STATUS_REFRESH_ICON),
    ) {
        Icon(
            modifier = Modifier.padding(8.dp),
            painter = painterResource(id = R.drawable.ic_refresh),
            contentDescription = null,
            tint = AppTheme.colors.button02a,
        )
    }
}
