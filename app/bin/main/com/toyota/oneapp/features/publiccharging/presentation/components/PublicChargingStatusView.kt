/*
 * Copyright © 2024. Toyota Motors North America Inc
 * All rights reserved.
 */

package com.toyota.oneapp.features.publiccharging.presentation.components

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.ColorFilter
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.toyota.oneapp.R
import com.toyota.oneapp.features.core.composable.OACallOut1TextView
import com.toyota.oneapp.features.core.composable.OASubHeadLine3TextView
import com.toyota.oneapp.features.core.composable.PrimaryButton02
import com.toyota.oneapp.features.core.theme.AppTheme
import com.toyota.oneapp.util.ToyotaConstants

@Composable
fun PublicChargingStatusContent(chargingStatus: ChargingStatus) {
    Box(Modifier.fillMaxSize()) {
        Column(
            modifier =
                Modifier
                    .fillMaxSize()
                    .background(AppTheme.colors.tertiary15)
                    .verticalScroll(rememberScrollState())
                    .padding(horizontal = 15.dp),
            horizontalAlignment = Alignment.CenterHorizontally,
        ) {
            Spacer(modifier = Modifier.height(8.dp))
            Image(
                painterResource(R.drawable.ic_drag),
                contentDescription = stringResource(R.string.swipeDownIconDescription),
                contentScale = ContentScale.FillWidth,
                modifier =
                    Modifier
                        .width(30.dp)
                        .height(10.dp),
                colorFilter = ColorFilter.tint(AppTheme.colors.outline01),
            )
            Spacer(modifier = Modifier.height(20.dp))
            OASubHeadLine3TextView(text = stringResource(R.string.charging_status), color = AppTheme.colors.tertiary03)
            Spacer(modifier = Modifier.height(18.dp))
            chargingStatus.chargePercentage?.let {
                val percentage = it.toFloatOrNull()?.div(100) ?: 0.0f
                ChargingPercentage(it, chargingStatus.isBatteryLow)
                Spacer(modifier = Modifier.height(8.dp))
                ChargingProgress(percentage)
            }
            ChargePluggedStatusAndTimeWidget(chargingStatus)
            ChargePointAddressTileWidget(
                address =
                    ChargePointAddress(
                        stationName = chargingStatus.chargePointAddress.stationName,
                        stationAddress = chargingStatus.chargePointAddress.stationAddress,
                    ),
            )
            with(chargingStatus.startTimeAndEnergy) {
                StartTimeAndEnergyWidget(
                    startTimeAndEnergy =
                        StartTimeAndEnergy(
                            time = time,
                            energyInKw = energyInKw,
                        ),
                )
            }
        }
        Column(
            Modifier
                .fillMaxWidth()
                .align(Alignment.BottomCenter)
                .padding(horizontal = 15.dp),
            horizontalAlignment = Alignment.CenterHorizontally,
        ) {
            RefreshIconWidget()
            OACallOut1TextView(
                text = stringResource(R.string.last_updated_time, chargingStatus.lastUpdatedTime.orEmpty()),
                color = AppTheme.colors.tertiary05,
            )
            Spacer(modifier = Modifier.height(12.dp))
            OACallOut1TextView(
                text = stringResource(R.string.stop_charging_info),
                color = AppTheme.colors.button02a,
                textAlign = TextAlign.Center,
            )
            Spacer(modifier = Modifier.height(12.dp))
            PrimaryButton02(
                modifier =
                    Modifier
                        .align(Alignment.CenterHorizontally)
                        .padding(bottom = 10.dp),
                text = stringResource(R.string.stop_charging),
                click = {},
            )
        }
    }
}

@Preview
@Composable
fun PublicChargingStatusContentPreview() {
    val chargingStatus =
        ChargingStatus(
            isCharging = true,
            isBatteryLow = false,
            chargePercentage = "80",
            chargeRunningStatus =
                ChargeRunningStatus(
                    isCharging = true,
                    chargingStatus = "Charging",
                    travelDistance = "100",
                    distanceUnit = "km",
                ),
            chargePointAddress =
                ChargePointAddress(
                    partnerName = "Partner Name",
                    stationName = "Station Name",
                    stationAddress = "Station Address",
                ),
            startTimeAndEnergy =
                StartTimeAndEnergy(
                    time = "1:00pm",
                    energyInKw = "0 kWh",
                ),
            lastUpdatedTime = "Jun 10 at 2:00pm",
            isEvGoPartnerAndStatusIsActive = true,
            isSessionInvalid = true,
            ctaStatus =
                ChargeCtaStatus(
                    isCtaActive = true,
                    ctaText = "Continue",
                ),
        )

    PublicChargingStatusContent(chargingStatus)
}

data class ChargingStatus(
    val isCharging: Boolean? = null,
    val isBatteryLow: Boolean? = null,
    val chargePercentage: String? = null,
    val chargeRunningStatus: ChargeRunningStatus,
    val chargePointAddress: ChargePointAddress,
    val startTimeAndEnergy: StartTimeAndEnergy,
    val lastUpdatedTime: String? = ToyotaConstants.EMPTY_STRING,
    val isEvGoPartnerAndStatusIsActive: Boolean? = null,
    val isSessionInvalid: Boolean? = null,
    val ctaStatus: ChargeCtaStatus,
    val estimatedTime: String? = null,
)

data class ChargeRunningStatus(
    val isCharging: Boolean? = null,
    val chargingStatus: String? = null,
    val travelDistance: String? = null,
    val distanceUnit: String? = null,
)

data class ChargePointAddress(
    val partnerName: String? = ToyotaConstants.EMPTY_STRING,
    val stationName: String? = ToyotaConstants.EMPTY_STRING,
    val stationAddress: String? = ToyotaConstants.EMPTY_STRING,
)

data class StartTimeAndEnergy(
    val time: String = ToyotaConstants.EMPTY_STRING,
    val energyInKw: String = ToyotaConstants.EMPTY_STRING,
)

data class ChargeCtaStatus(
    val isCtaActive: Boolean? = null,
    val ctaText: String? = null,
)
