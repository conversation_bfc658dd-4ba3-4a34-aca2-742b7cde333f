package com.toyota.oneapp.features.publiccharging.presentation

import android.annotation.SuppressLint
import android.location.Geocoder
import androidx.activity.compose.BackHandler
import androidx.compose.foundation.Image
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.BoxWithConstraints
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.layout.wrapContentWidth
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.BottomSheetScaffold
import androidx.compose.material.BottomSheetValue
import androidx.compose.material.Divider
import androidx.compose.material.Icon
import androidx.compose.material.rememberBottomSheetScaffoldState
import androidx.compose.material.rememberBottomSheetState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.MutableState
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.ColorFilter
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.input.TextFieldValue
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavController
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import androidx.navigation.compose.rememberNavController
import com.google.android.gms.maps.model.LatLng
import com.toyota.oneapp.R
import com.toyota.oneapp.features.chargeinfo.entrollment.presentation.EnrollmentViewModel
import com.toyota.oneapp.features.core.composable.CurrentLocationRequest
import com.toyota.oneapp.features.core.composable.OABody4TextView
import com.toyota.oneapp.features.core.composable.OACallOut1TextView
import com.toyota.oneapp.features.core.composable.PrimaryButton02
import com.toyota.oneapp.features.core.composable.ShowProgressIndicator
import com.toyota.oneapp.features.core.navigation.OAScreen
import com.toyota.oneapp.features.core.theme.AppTheme
import com.toyota.oneapp.features.find.utils.LocationUtil
import com.toyota.oneapp.features.findstations.application.distanceBetween
import com.toyota.oneapp.features.findstations.presentation.EmptyStationsContent
import com.toyota.oneapp.features.findstations.presentation.SearchTextField
import com.toyota.oneapp.features.findstations.presentation.SendToCarModalDialog
import com.toyota.oneapp.features.findstations.presentation.StationsHeader
import com.toyota.oneapp.features.ftue.presentation.extension.h
import com.toyota.oneapp.features.publiccharging.application.PublicChargingState
import com.toyota.oneapp.features.publiccharging.application.getPlugValue
import com.toyota.oneapp.features.publiccharging.domain.model.ChargeStationInfo
import com.toyota.oneapp.features.publiccharging.util.PublicChargingConstants
import com.toyota.oneapp.util.ToyUtil
import java.util.Locale

@SuppressLint("UnusedBoxWithConstraintsScope")
@Composable
fun PublicChargingScreen(
    navController: NavController,
    modifier: Modifier = Modifier,
    publicChargingViewModel: PublicChargingViewModel = hiltViewModel(),
    enrollmentViewModel: EnrollmentViewModel = hiltViewModel(),
) {
    val sheetState =
        rememberBottomSheetState(
            initialValue = BottomSheetValue.Collapsed,
        )

    val scaffoldState = rememberBottomSheetScaffoldState(bottomSheetState = sheetState)

    var currentLocation by remember { mutableStateOf(LocationUtil.getDefaultLocation()) }

    BoxWithConstraints {
        val configuration = LocalConfiguration.current
        val screenHeight = configuration.screenHeightDp
        val peekHeight = 0.6 * screenHeight
        BottomSheetScaffold(
            sheetPeekHeight = peekHeight.dp,
            sheetShape = RoundedCornerShape(topStart = 30.dp, topEnd = 30.dp),
            scaffoldState = scaffoldState,
            sheetContent = {
                val stationNavController = rememberNavController()
                NavHost(
                    navController = stationNavController,
                    startDestination = OAScreen.ChargeStationListScreen.route,
                ) {
                    composable(OAScreen.ChargeStationListScreen.route) {
                        StationsBottomSheetContent(
                            peekHeight = peekHeight,
                            screenHeight = screenHeight,
                            mainNavController = navController,
                            stationNavController = stationNavController,
                            viewModel = publicChargingViewModel,
                        )
                    }
                    composable(OAScreen.ChargeStationDetailsScreen.route) {
                        StationDetailsScreen(
                            stationNavController = stationNavController,
                            viewModel = publicChargingViewModel,
                            enrollmentViewModel = enrollmentViewModel,
                        )
                    }
                }
                SendToCarModalDialog(
                    sendToCarState = publicChargingViewModel.sendToCarState.collectAsState().value,
                    onDismiss = { publicChargingViewModel.hideSendToCarSuccess() },
                )
            },
            content = {
                StationMapView(
                    currentLocation = currentLocation,
                    viewModel = publicChargingViewModel,
                )
            },
            modifier = modifier.fillMaxSize(),
        )

        CurrentLocationRequest { location ->
            currentLocation = location
            publicChargingViewModel.fetchStations(LocationUtil.getPosition(location))
        }
    }
}

@Composable
fun StationsBottomSheetContent(
    peekHeight: Double,
    screenHeight: Int,
    mainNavController: NavController,
    stationNavController: NavController,
    viewModel: PublicChargingViewModel,
    modifier: Modifier = Modifier,
) {
    val stationState = viewModel.stationState.collectAsState().value

    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
        modifier =
            modifier
                .fillMaxWidth()
                .fillMaxHeight()
                .heightIn(
                    min = peekHeight.dp,
                    max = (0.5 * screenHeight).dp,
                )
                .padding(all = 8.dp),
    ) {
        Image(
            painter = painterResource(id = R.drawable.ic_drag_indicator),
            contentDescription = stringResource(id = R.string.content_drag),
            modifier =
                Modifier
                    .width(28.dp)
                    .height(4.dp),
            colorFilter = ColorFilter.tint(AppTheme.colors.outline01),
        )
        StationsHeader(
            title = stringResource(id = R.string.nearby_stations),
        ) {
            mainNavController.popBackStack()
        }

        val searchText = remember { mutableStateOf(TextFieldValue("")) }
        when (stationState) {
            is PublicChargingState.Loading -> {
                ShowProgressIndicator(dialogState = true)
            }

            is PublicChargingState.EmptyStations -> {
                EmptyStationsContent(
                    isH2Vehicle = false,
                    navController = mainNavController,
                    searchText = searchText,
                    modifier =
                        Modifier
                            .padding(horizontal = 8.dp),
                ) { _, _ ->
                }
            }

            is PublicChargingState.Success -> {
                PublicStationsListView(
                    mainNavController = mainNavController,
                    stationNavController = stationNavController,
                    uiModel = stationState.data,
                    searchText = searchText,
                    selectedLocation = stationState.selectedLatLng,
                ) { _, _ ->
                }
            }

            else -> {
                return
            }
        }
    }
}

@Composable
fun PublicStationsListView(
    mainNavController: NavController,
    stationNavController: NavController,
    uiModel: List<ChargeStationInfo>,
    searchText: MutableState<TextFieldValue>,
    selectedLocation: LatLng?,
    onFetchStations: (geocoder: Geocoder, searchText: String) -> Unit,
) {
    val context = LocalContext.current

    BackHandler {
        mainNavController.popBackStack()
    }
    SearchTextField(
        searchText = searchText,
    ) { searchText ->
        onFetchStations(Geocoder(context, Locale.getDefault()), searchText)
    }

    FilterMenu()
    LazyColumn {
        items(uiModel) { stationItem ->
            PublicStationItems(stationItem, selectedLocation) {
                stationNavController.currentBackStackEntry?.savedStateHandle?.apply {
                    set(PublicChargingConstants.STATION_DATA, stationItem)
                    set(PublicChargingConstants.SELECTED_LOCATION, selectedLocation)
                }
                stationNavController.navigate(
                    OAScreen.ChargeStationDetailsScreen.route,
                )
            }
        }
    }
}

@Composable
fun PublicStationItems(
    data: ChargeStationInfo,
    selectedLocation: LatLng?,
    modifier: Modifier = Modifier,
    onClick: () -> Unit,
) {
    Column(
        modifier =
            modifier
                .padding(horizontal = 8.dp, vertical = 8.dp)
                .clickable { onClick() }
                .fillMaxHeight(),
    ) {
        StationAddressCard(data, selectedLocation)
        Spacer(modifier = Modifier.height(8.dp))
        Row(
            horizontalArrangement = Arrangement.End,
        ) {
            if (data.evIsPartner) {
                val plugAvailability = data.evConnectorSum?.getPlugValue()
                if (plugAvailability?.isNotEmpty() == true) {
                    LazyRow(
                        modifier = Modifier.weight(0.4f),
                    ) {
                        items(plugAvailability) { item ->
                            PlugTypeChip(
                                item[0],
                                item[1],
                            )
                        }
                    }
                }
            } else {
                OABody4TextView(
                    text = stringResource(R.string.out_of_network),
                    color = AppTheme.colors.tertiary03,
                    modifier = Modifier.weight(0.8f),
                )
            }
            Row(
                horizontalArrangement = Arrangement.SpaceBetween,
            ) {
                Icon(
                    painter = painterResource(id = R.drawable.map_car),
                    contentDescription = null,
                    modifier = Modifier.padding(top = 6.dp),
                )
                Spacer(modifier = Modifier.width(16.dp))
                Icon(
                    painter = painterResource(id = R.drawable.ic_detail_direction),
                    contentDescription = null,
                    modifier = Modifier.padding(top = 8.dp),
                )
                Spacer(modifier = Modifier.width(16.dp))
                Icon(
                    painter = painterResource(id = R.drawable.ic_favorite_icon),
                    contentDescription = null,
                    modifier = Modifier.padding(top = 12.dp),
                )
            }
        }

        Divider(
            color = AppTheme.colors.tertiary10,
            thickness = 1.dp,
            modifier =
                Modifier
                    .fillMaxWidth(),
        )
    }
}

@Composable
fun StationDetailsScreen(
    stationNavController: NavController,
    viewModel: PublicChargingViewModel,
    modifier: Modifier = Modifier,
    enrollmentViewModel: EnrollmentViewModel,
) {
    val data =
        stationNavController.previousBackStackEntry?.savedStateHandle?.get<ChargeStationInfo>(
            PublicChargingConstants.STATION_DATA,
        )
    val selectedLocation =
        stationNavController.previousBackStackEntry?.savedStateHandle?.get<LatLng>(
            PublicChargingConstants.SELECTED_LOCATION,
        )
    val context = LocalContext.current
    Column(
        modifier =
            modifier
                .fillMaxWidth()
                .padding(all = 8.dp),
    ) {
        Image(
            painter = painterResource(id = R.drawable.ic_drag_indicator),
            contentDescription = stringResource(id = R.string.content_drag),
            modifier =
                Modifier
                    .width(28.dp)
                    .height(4.dp),
            colorFilter = ColorFilter.tint(AppTheme.colors.outline01),
        )

        StationsHeader(
            title = stringResource(R.string.in_network),
        ) {
            stationNavController.popBackStack()
        }
        if (data != null && selectedLocation != null) {
            StationAddressCard(data, selectedLocation)
            Spacer(modifier = Modifier.height(8.dp))
        }
        ActionCard(onSendToCarClick = {
            data?.let { viewModel.sendPOIToCar(it) }
        }, onDirectionClick = {
            selectedLocation?.let {
                ToyUtil.openGoogleMap(
                    context = context,
                    query = "${it.latitude},${it.longitude}",
                )
            }
        })
        AvailablePlugs(data)
        Spacer(Modifier.height(16.h()))
        val buttonText =
            viewModel
                .mapStationButtonTitle(
                    data,
                    enrollmentViewModel.enrollmentState,
                ).orEmpty()
        PrimaryButton02(
            text = buttonText,
            modifier =
                Modifier
                    .wrapContentWidth()
                    .wrapContentHeight()
                    .align(Alignment.CenterHorizontally),
            click = {
            },
        )
    }
}

@Composable
fun StationAddressCard(
    data: ChargeStationInfo,
    selectedLocation: LatLng?,
) {
    Row(
        horizontalArrangement = Arrangement.End,
    ) {
        data.stationName?.let { stationName ->
            OABody4TextView(
                text = stationName,
                color = AppTheme.colors.tertiary03,
                modifier = Modifier.weight(0.8f),
            )
        }
        Spacer(modifier = Modifier.width(8.dp))
        if (selectedLocation != null && data.markerInfo?.stationPosition != null) {
            OACallOut1TextView(
                text = selectedLocation.distanceBetween(data.markerInfo.stationPosition, false),
                color = AppTheme.colors.tertiary05,
                maxLines = 1,
            )
        }
    }

    Spacer(modifier = Modifier.height(8.dp))

    data.addressLine1?.let { addressLine1 ->
        OACallOut1TextView(
            text = addressLine1,
            color = AppTheme.colors.tertiary05,
            maxLines = 1,
        )
    }
    data.addressLine2?.let { addressLine2 ->
        OACallOut1TextView(
            text = addressLine2,
            color = AppTheme.colors.tertiary05,
            maxLines = 1,
        )
    }
    if (data.is24hoursOpen) {
        OACallOut1TextView(
            text = stringResource(R.string.open_24_hours),
            color = AppTheme.colors.secondary01,
        )
    }
}

@Composable
fun ActionCard(
    onSendToCarClick: () -> Unit,
    onDirectionClick: () -> Unit,
) {
    Row(
        modifier =
            Modifier
                .fillMaxWidth()
                .padding(horizontal = 24.dp),
        horizontalArrangement = Arrangement.SpaceEvenly,
    ) {
        Column {
            CircleImageView(R.drawable.ic_pricing) {
            }
            OACallOut1TextView(
                text = stringResource(R.string.pricing),
                color = AppTheme.colors.tertiary03,
                modifier = Modifier.padding(start = 12.dp, bottom = 12.dp),
            )
        }
        Spacer(modifier = Modifier.width(16.dp))
        Column {
            CircleImageView(R.drawable.map_car) {
                onSendToCarClick()
            }
            OACallOut1TextView(
                text = stringResource(R.string.share_poi_send),
                color = AppTheme.colors.tertiary03,
                modifier = Modifier.padding(bottom = 12.dp),
            )
        }
        Spacer(modifier = Modifier.width(16.dp))
        Column {
            CircleImageView(R.drawable.ic_detail_direction) {
                onDirectionClick()
            }
            OACallOut1TextView(
                text = stringResource(R.string.directions_card),
                color = AppTheme.colors.tertiary03,
                modifier = Modifier.padding(start = 8.dp, bottom = 12.dp),
            )
        }
    }
}
