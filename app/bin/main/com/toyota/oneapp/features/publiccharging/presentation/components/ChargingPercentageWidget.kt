/*
 * Copyright © 2024. Toyota Motors North America IncAll rights reserved.
 */

package com.toyota.oneapp.features.publiccharging.presentation.components

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Row
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.toyota.oneapp.features.core.composable.OASubHeadLine4TextView
import com.toyota.oneapp.features.core.composable.OATextTitle2TextView
import com.toyota.oneapp.features.core.theme.AppTheme

@Composable
fun ChargingPercentage(
    percentage: String,
    batteryLow: Boolean?,
) {
    Row(
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.spacedBy(8.dp),
    ) {
        batteryLow?.let {
            ChargingIcon(isBatteryLow = it)
        }
        Row(verticalAlignment = Alignment.Bottom) {
            OATextTitle2TextView(
                text = percentage,
                color = AppTheme.colors.tertiary00,
                textAlign = TextAlign.Start,
            )
            OASubHeadLine4TextView(text = "%", color = AppTheme.colors.tertiary00)
        }
    }
}

@Preview
@Composable
fun ChargingPercentagePreview() {
    ChargingPercentage("100", false)
}
