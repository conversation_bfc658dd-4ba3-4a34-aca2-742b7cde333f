package com.toyota.oneapp.features.publiccharging.application

import com.google.android.gms.maps.model.LatLng
import com.toyota.oneapp.features.findstations.dataaccess.servermodel.Connectors
import com.toyota.oneapp.features.findstations.dataaccess.servermodel.Elements
import com.toyota.oneapp.features.findstations.dataaccess.servermodel.Evses
import com.toyota.oneapp.features.findstations.dataaccess.servermodel.PriceComponents
import com.toyota.oneapp.features.findstations.dataaccess.servermodel.Station
import com.toyota.oneapp.features.findstations.dataaccess.servermodel.TariffInfo
import com.toyota.oneapp.features.findstations.domain.model.MarkerInfo
import com.toyota.oneapp.features.findstations.util.FindStationsConstants.FCV_RADIUS
import com.toyota.oneapp.features.findstations.util.FindStationsConstants.PHEV_RADIUS
import com.toyota.oneapp.features.publiccharging.domain.model.ChargeSessionData
import com.toyota.oneapp.features.publiccharging.domain.model.ChargeStationInfo
import com.toyota.oneapp.features.publiccharging.domain.model.Coordinates
import com.toyota.oneapp.features.publiccharging.domain.model.EvConnector
import com.toyota.oneapp.features.publiccharging.domain.model.EvConnectorDetails
import com.toyota.oneapp.features.publiccharging.domain.model.EvConnectorSum
import com.toyota.oneapp.features.publiccharging.domain.model.EvEVSE
import com.toyota.oneapp.features.publiccharging.domain.model.EvOpeningTimes
import com.toyota.oneapp.features.publiccharging.domain.model.EvPriceComponent
import com.toyota.oneapp.features.publiccharging.domain.model.EvPriceElement
import com.toyota.oneapp.features.publiccharging.domain.model.EvTariffInfo
import com.toyota.oneapp.features.publiccharging.domain.model.StartChargingRequest
import com.toyota.oneapp.features.publiccharging.domain.model.StopChargingData
import com.toyota.oneapp.features.publiccharging.domain.model.StopChargingRequest
import com.toyota.oneapp.features.publiccharging.domain.repo.PublicChargingRepo
import com.toyota.oneapp.features.publiccharging.util.PublicChargingConstants
import com.toyota.oneapp.features.publiccharging.util.PublicChargingConstants.DAILY_OPEN_24_HOURS
import com.toyota.oneapp.features.publiccharging.util.PublicChargingConstants.DAY_OPEN_24_HOURS
import com.toyota.oneapp.features.publiccharging.util.PublicChargingConstants.WEEKLY_OPEN_24_HOURS
import com.toyota.oneapp.model.vehicle.Feature
import com.toyota.oneapp.model.vehicle.VehicleInfo
import com.toyota.oneapp.model.vehicle.VehicleInfo.FUELTYPE_HYDROGENFUELCELL
import com.toyota.oneapp.network.Resource
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import java.util.UUID
import javax.inject.Inject

class PublicChargingLogic
    @Inject
    constructor(
        private val publicChargingRepo: PublicChargingRepo,
    ) : PublicChargingUseCase {
        override fun fetchNearByStations(
            lat: Double?,
            long: Double?,
            vehicleInfo: VehicleInfo,
        ): Flow<List<ChargeStationInfo>> {
            return flow {
                if (vehicleInfo.isFeatureEnabled(Feature.EV_PUBLIC_CHARGING_CONTROL)) {
                    val response =
                        publicChargingRepo.fetchNearbyStations(
                            brand = vehicleInfo.brand,
                            fuelType = vehicleInfo.fuelType,
                            radius =
                                if (vehicleInfo.fuelType == FUELTYPE_HYDROGENFUELCELL) {
                                    FCV_RADIUS
                                } else {
                                    PHEV_RADIUS
                                },
                            region = vehicleInfo.region,
                            latitude = lat,
                            longitude = long,
                        )
                    response.data?.payload?.stations?.let {
                        emit(it.toUiModel())
                    } ?: run {
                        emit(ArrayList())
                    }
                }
            }
        }

        override fun startCharging(
            vehicleInfo: VehicleInfo,
            country: String,
            channel: String,
            locale: String,
            startChargingRequest: StartChargingRequest,
        ): Flow<String?> {
            return flow {
                val response =
                    publicChargingRepo.startCharging(
                        make = vehicleInfo.make,
                        country = country,
                        channel = channel,
                        correlationid = UUID.randomUUID().toString(),
                        locale = locale,
                        vin = vehicleInfo.vin,
                        startChargingRequest = startChargingRequest,
                    )
                if (response is Resource.Success) {
                    emit(response.data?.startChargingPayload?.startChargeResponse?.chargingId)
                } else {
                    emit(null)
                }
            }
        }

        override fun stopCharging(
            vehicleInfo: VehicleInfo,
            channel: String,
            locale: String,
            stopChargingRequest: StopChargingRequest,
        ): Flow<StopChargingData?> {
            return flow {
                val response =
                    publicChargingRepo.stopCharging(
                        make = vehicleInfo.make,
                        channel = channel,
                        correlationid = UUID.randomUUID().toString(),
                        locale = locale,
                        vin = vehicleInfo.vin,
                        stopChargingRequest = stopChargingRequest,
                    )
                emit(
                    response.data,
                )
            }
        }

        override fun fetchChargeSession(
            chargingId: String,
            vehicleInfo: VehicleInfo,
            channel: String,
            locale: String,
        ): Flow<ChargeSessionData?> {
            return flow {
                val response =
                    publicChargingRepo.fetchChargeSession(
                        chargingId = chargingId,
                        locale = locale,
                        make = vehicleInfo.make,
                        channel = channel,
                        correlationid = UUID.randomUUID().toString(),
                        vin = vehicleInfo.vin,
                    )
                emit(
                    response.data,
                )
            }
        }
    }

fun List<Station>.toUiModel(): List<ChargeStationInfo> {
    val data = ArrayList<ChargeStationInfo>()
    this.forEach {
        data.add(
            ChargeStationInfo(
                stationName = it.operator?.name,
                addressLine1 = it.name,
                addressLine2 = "${it.address}, ${it.city}",
                markerInfo = it.geometry?.coordinates.getMarkerInfo(it.name.orEmpty()),
                is24hoursOpen = it.openingTimes?.timing.is24hoursTiming(),
                evId = it.id,
                evName = it.name,
                evTariffInfo = mapTariffInfo(it.tariffInfo),
                evPhoneNumber = it.phoneNumber,
                evPlaceId = it.placeId,
                evPostalCode = it.postalCode,
                evProvince = it.province,
                evAddress = it.address,
                evCity = it.city,
                evStatusCode = it.statusCode,
                evStatusSum = it.statusSum,
                evTimeZone = it.timeZone,
                evEvses = mapEves(it.partnerInfo?.firstOrNull()?.evses ?: emptyList()),
                evEvDcFastNum = it.evDCFastNum,
                evEvLevel1EvseNum = it.evLevel1,
                evEvLevel2EvseNum = it.evLevel2,
                evIsPartner = it.isPartner == true,
                evEvSource = it.evSource.orEmpty(),
                evOpeningTimes =
                    EvOpeningTimes(
                        evRegularHour = it.openingTimes?.regularHour,
                        evTiming = it.openingTimes?.timing,
                    ),
                evConnectorSum =
                    EvConnectorSum(
                        evCcs1 =
                            EvConnectorDetails(
                                it.connectorSum?.ccs1?.total,
                                it.connectorSum?.ccs1?.active,
                            ),
                        evJ1772 =
                            EvConnectorDetails(
                                it.connectorSum?.j1772?.total,
                                it.connectorSum?.j1772?.active,
                            ),
                        evNacs =
                            EvConnectorDetails(
                                it.connectorSum?.nacs?.total,
                                it.connectorSum?.nacs?.active,
                            ),
                        evChademo =
                            EvConnectorDetails(
                                it.connectorSum?.chademo?.total,
                                it.connectorSum?.chademo?.active,
                            ),
                    ),
            ),
        )
    }
    return data
}

fun EvConnectorSum.getPlugValue(): List<List<String>> {
    val plugType = ArrayList<ArrayList<String>>()

    fun addPlug(
        plugTypeName: String,
        connector: EvConnectorDetails?,
    ) {
        connector?.takeIf { it.total != null && it.total != 0 }?.let {
            val plugValue = arrayListOf(plugTypeName, "${it.active ?: 0}/${it.total}")
            plugType.add(plugValue)
        }
    }
    addPlug(PublicChargingConstants.DC_FAST, evCcs1)
    addPlug(PublicChargingConstants.LEVEL_2, evJ1772)
    addPlug(PublicChargingConstants.CHA_DEMO, evChademo)

    return plugType
}

fun List<Double>?.getMarkerInfo(stationName: String): MarkerInfo? {
    if (this != null && this.size >= 2) {
        return MarkerInfo(
            LatLng(this[1], this[0]),
            stationName,
        )
    }
    return null
}

fun String?.is24hoursTiming(): Boolean {
    return this in setOf(DAY_OPEN_24_HOURS, DAILY_OPEN_24_HOURS, WEEKLY_OPEN_24_HOURS)
}

fun mapPriceElements(elements: ArrayList<Elements>?): List<EvPriceElement>? {
    return elements?.map { elem ->
        EvPriceElement(
            evPriceComponents = mapPriceComponents(elem.priceComponents),
        )
    }
}

fun mapTariffInfo(tariff: TariffInfo?): EvTariffInfo {
    return EvTariffInfo(
        evCurrency = tariff?.currency,
        evElements = mapPriceElements(tariff?.elements),
        evId = tariff?.id,
        evPartnerName = tariff?.partnerName,
        evTariffAltURL = tariff?.tariffAltUrl,
    )
}

private fun mapPriceComponents(components: ArrayList<PriceComponents>): List<EvPriceComponent>? {
    return components.map { component ->
        EvPriceComponent(
            evPrice = component.price,
            evStepSize = component.stepSize,
            evType = component.type,
        )
    }
}

private fun mapEves(evse: List<Evses>): List<EvEVSE> {
    return evse.map { evse ->
        EvEVSE(
            uid = evse.uid,
            evseId = evse.evseId,
            status = evse.status,
            capabilities = evse.capabilities,
            connectors = mapConnectors(evse.connectors),
            coordinates =
                Coordinates(
                    evLatitude = evse.coordinates?.latitude,
                    evLongitude = evse.coordinates?.longitude,
                ),
            floorLevel = evse.physicalReference,
            physicalReference = evse.physicalReference,
        )
    }
}

private fun mapConnectors(con: List<Connectors>): List<EvConnector> {
    return con.map { connector ->
        EvConnector(
            evAmperage = connector.amperage,
            evChargerLevel = connector.chargerLevel,
            evChargerType = connector.chargerType,
            evFormat = connector.format,
            evId = connector.id,
            evLastUpdated = connector.lastUpdated,
            evMaxPower = connector.maxPower?.toDouble(),
            evPowerType = connector.powerType,
            evStandard = connector.standard,
            evTariffId = connector.tariffId,
            evVoltage = connector.voltage,
        )
    }
}
