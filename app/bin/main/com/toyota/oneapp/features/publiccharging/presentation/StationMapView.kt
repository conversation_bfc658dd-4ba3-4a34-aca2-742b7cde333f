package com.toyota.oneapp.features.publiccharging.presentation

import android.location.Location
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import com.google.android.gms.maps.model.CameraPosition
import com.google.android.gms.maps.model.LatLng
import com.google.maps.android.compose.rememberCameraPositionState
import com.toyota.oneapp.features.find.utils.LocationUtil
import com.toyota.oneapp.features.findstations.presentation.StationsGoogleMap
import com.toyota.oneapp.features.findstations.presentation.StationsMapIcons
import com.toyota.oneapp.features.findstations.util.FindStationsConstants.DEFAULT_LAT_LNG
import com.toyota.oneapp.features.publiccharging.application.PublicChargingState
import com.toyota.oneapp.features.publiccharging.domain.model.ChargeStationInfo

@Composable
fun StationMapView(
    currentLocation: Location,
    modifier: Modifier = Modifier,
    viewModel: PublicChargingViewModel,
) {
    val mapViewState = viewModel.stationState.collectAsState().value
    val cameraPositionState = rememberCameraPositionState()
    var canShowVehicleLocationMapIcon by remember { mutableStateOf(false) }

    var stationsList: List<ChargeStationInfo> by remember { mutableStateOf(emptyList()) }

    Box(
        modifier =
            modifier
                .fillMaxWidth()
                .fillMaxHeight(0.5f),
    ) {
        var vehicleLocation: LatLng? by remember { mutableStateOf(null) }

        if (vehicleLocation != null && vehicleLocation?.latitude != DEFAULT_LAT_LNG &&
            vehicleLocation?.longitude != DEFAULT_LAT_LNG
        ) {
            cameraPositionState.position =
                CameraPosition.fromLatLngZoom(
                    vehicleLocation ?: LocationUtil.getPosition(LocationUtil.getDefaultLocation()),
                    15f,
                )
        } else {
            cameraPositionState.position =
                CameraPosition.fromLatLngZoom(
                    LocationUtil.getPosition(currentLocation),
                    15f,
                )
        }
        when (mapViewState) {
            is PublicChargingState.Success -> {
                stationsList = mapViewState.data
            }

            else -> {
                return
            }
        }
        StationsGoogleMap(
            cameraPositionState = cameraPositionState,
            vehicleLocation = vehicleLocation,
            currentLocation = LocationUtil.getPosition(currentLocation),
            markerInfo = stationsList.mapNotNull { it.markerInfo },
            modifier = modifier,
        ) { _ ->
        }

        StationsMapIcons(
            canShowVehicleLocationMapIcon = canShowVehicleLocationMapIcon,
            modifier =
                Modifier
                    .wrapContentSize()
                    .align(Alignment.BottomEnd)
                    .padding(end = 16.dp, bottom = 94.dp),
            vehicleLocationOnClick = {
            },
            currentLocationOnClick = {
            },
        )
    }
}
