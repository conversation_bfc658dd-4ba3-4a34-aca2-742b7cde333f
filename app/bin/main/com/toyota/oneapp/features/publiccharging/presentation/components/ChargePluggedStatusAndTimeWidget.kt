/*
 * Copyright © 2024. Toyota Motors North America Inc
 * All rights reserved.
 */

package com.toyota.oneapp.features.publiccharging.presentation.components

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.width
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import com.toyota.oneapp.R
import com.toyota.oneapp.features.core.composable.OACallOut1TextView
import com.toyota.oneapp.features.core.composable.OATextTitle2TextView
import com.toyota.oneapp.features.core.composable.OATextTitle3
import com.toyota.oneapp.features.core.theme.AppTheme

@Composable
fun ChargePluggedStatusAndTimeWidget(chargingStatus: ChargingStatus) {
    Column(horizontalAlignment = Alignment.CenterHorizontally) {
        Spacer(modifier = Modifier.height(28.dp))
        chargingStatus.chargeRunningStatus.isCharging?.let {
            chargingStatus.chargeRunningStatus.chargingStatus?.let {
                LowChargingIndicationWidget(it)
            }
        } ?: Row(verticalAlignment = Alignment.CenterVertically) {
            val distanceToEmpty = chargingStatus.chargeRunningStatus.travelDistance ?: "--"
            val unit = chargingStatus.chargeRunningStatus.distanceUnit.orEmpty()
            OATextTitle3(text = distanceToEmpty, color = AppTheme.colors.tertiary00)
            Spacer(modifier = Modifier.width(4.dp))
            OATextTitle2TextView(text = unit, color = AppTheme.colors.tertiary00, textAlign = TextAlign.Left)
        }
        Spacer(modifier = Modifier.height(18.dp))
        OACallOut1TextView(
            text =
                stringResource(
                    if (chargingStatus.isBatteryLow == true) {
                        R.string.time_until_full
                    } else {
                        R.string.time_until_fully_charged
                    },
                    chargingStatus.estimatedTime.orEmpty(),
                ),
            color = AppTheme.colors.tertiary05,
        )
        Spacer(modifier = Modifier.height(65.dp))
    }
}
