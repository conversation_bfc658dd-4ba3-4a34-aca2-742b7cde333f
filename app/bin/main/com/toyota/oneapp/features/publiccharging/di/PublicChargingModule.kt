package com.toyota.oneapp.features.publiccharging.di

import com.toyota.oneapp.features.publiccharging.application.PublicChargingLogic
import com.toyota.oneapp.features.publiccharging.application.PublicChargingUseCase
import com.toyota.oneapp.features.publiccharging.dataaccess.repo.PublicChargingDefaultRepo
import com.toyota.oneapp.features.publiccharging.domain.repo.PublicChargingRepo
import dagger.Binds
import dagger.Module
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent

@Module
@InstallIn(SingletonComponent::class)
abstract class PublicChargingModule {
    @Binds
    abstract fun bindPublicChargingRepo(repo: PublicChargingDefaultRepo): PublicChargingRepo

    @Binds
    abstract fun bindPublicChargingUseCase(logic: PublicChargingLogic): PublicChargingUseCase
}
