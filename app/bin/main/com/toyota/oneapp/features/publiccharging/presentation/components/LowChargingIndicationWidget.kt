/*
 * Copyright © 2024. Toyota Motors North America IncAll rights reserved.\n
 */

package com.toyota.oneapp.features.publiccharging.presentation.components

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material.Surface
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.toyota.oneapp.features.core.composable.OACallOut1TextView
import com.toyota.oneapp.features.core.theme.AppTheme

@Composable
fun LowChargingIndicationWidget(status: String) {
    Box(
        modifier =
            Modifier
                .width(87.dp)
                .height(34.dp),
    ) {
        Surface(
            shape = CircleShape,
            color = AppTheme.colors.primary02,
            modifier =
                Modifier
                    .fillMaxSize(),
        ) {
            Box(
                contentAlignment = Alignment.Center,
                modifier = Modifier.fillMaxSize(),
            ) {
                OACallOut1TextView(
                    text = status,
                    color = AppTheme.colors.primary01,
                    textAlign = TextAlign.Center,
                )
            }
        }
    }
}

@Preview(showBackground = true)
@Composable
fun LowChargingIndicationWidgetPreview() {
    LowChargingIndicationWidget("charging")
}
