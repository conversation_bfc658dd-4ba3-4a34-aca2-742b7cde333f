package com.toyota.oneapp.features.publiccharging.presentation

import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.res.stringResource
import com.toyota.oneapp.R

@Composable
fun FilterMenu() {
    var selectedFilter by remember { mutableStateOf<FilterMenuData?>(null) }
    selectedFilter?.let { filterName ->
        FilterScreen(filterName)
    }
    val filterList = ArrayList<FilterMenuData>()
    val partnerList = ArrayList<FilterData>()
    partnerList.add(
        FilterData(
            R.string.partnerFilterEVConnect,
        ),
    )
    partnerList.add(FilterData(R.string.partnerFilterEVgo))
    partnerList.add(
        FilterData(
            R.string.partnerFilterFLONetwork,
        ),
    )
    partnerList.add(
        FilterData(
            R.string.partnerFilterGreenlots,
        ),
    )
    partnerList.add(
        FilterData(
            R.string.partnerFilterShellRecharge,
        ),
    )
    partnerList.add(
        FilterData(
            R.string.chargePointLowerCase,
        ),
    )
    var plugList = ArrayList<FilterData>()
    plugList.add(
        FilterData(
            R.string.level_2,
        ),
    )
    plugList.add(
        FilterData(
            R.string.dcfast,
        ),
    )
    filterList.add(
        FilterMenuData(stringResource(R.string.partners), partnerList),
    )

    filterList.add(
        FilterMenuData(stringResource(R.string.plug_types), plugList),
    )
    filterList.add(
        FilterMenuData(stringResource(R.string.favorites), ArrayList()),
    )
    filterList.add(
        FilterMenuData(stringResource(R.string.clear_all), ArrayList()),
    )
    LazyRow {
        items(filterList) { item ->
            StationFilterChip(
                filterMenuData = item,
                onClick = {
                    selectedFilter = item
                },
            )
        }
    }
}

data class FilterMenuData(
    val filterType: String,
    val filterData: ArrayList<FilterData>,
)

data class FilterData(
    val filterName: Int,
) {
    val isSelected: Boolean = false
}
