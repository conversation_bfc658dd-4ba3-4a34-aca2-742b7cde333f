package com.toyota.oneapp.features.publiccharging.dataaccess.repo

import com.toyota.oneapp.features.findstations.dataaccess.servermodel.StationsListResponse
import com.toyota.oneapp.features.publiccharging.dataaccess.service.PublicChargingApi
import com.toyota.oneapp.features.publiccharging.domain.model.ChargeSessionData
import com.toyota.oneapp.features.publiccharging.domain.model.StartChargingData
import com.toyota.oneapp.features.publiccharging.domain.model.StartChargingRequest
import com.toyota.oneapp.features.publiccharging.domain.model.StopChargingData
import com.toyota.oneapp.features.publiccharging.domain.model.StopChargingRequest
import com.toyota.oneapp.features.publiccharging.domain.repo.PublicChargingRepo
import com.toyota.oneapp.network.BaseRepository
import com.toyota.oneapp.network.ErrorMessageParser
import com.toyota.oneapp.network.Resource
import javax.inject.Inject
import kotlin.coroutines.CoroutineContext

class PublicChargingDefaultRepo
    @Inject
    constructor(
        private val publicChargingApi: PublicChargingApi,
        errorParser: ErrorMessageParser,
        ioContext: CoroutineContext,
    ) : BaseRepository(errorParser, ioContext), PublicChargingRepo {
        override suspend fun fetchNearbyStations(
            brand: String,
            fuelType: String,
            radius: Int,
            region: String,
            latitude: Double?,
            longitude: Double?,
        ): Resource<StationsListResponse?> {
            return makeApiCall {
                publicChargingApi.fetchAllNearbyStations(
                    brand = brand,
                    fuelType = fuelType,
                    radius = radius,
                    region = region,
                    latitude = latitude,
                    longitude = longitude,
                )
            }
        }

        override suspend fun startCharging(
            make: String,
            country: String,
            channel: String,
            correlationid: String,
            locale: String,
            vin: String,
            startChargingRequest: StartChargingRequest,
        ): Resource<StartChargingData?> {
            return makeApiCall {
                publicChargingApi.startCharging(
                    xCountry = country,
                    xChannel = channel,
                    xCorrelationid = correlationid,
                    xLocale = locale,
                    xVin = vin,
                    startChargingRequest = startChargingRequest,
                )
            }
        }

        override suspend fun stopCharging(
            make: String,
            channel: String,
            correlationid: String,
            locale: String,
            vin: String,
            stopChargingRequest: StopChargingRequest,
        ): Resource<StopChargingData?> {
            return makeApiCall {
                publicChargingApi.stopCharging(
                    xMake = make,
                    xChannel = channel,
                    xCorrelationid = correlationid,
                    xLocale = locale,
                    xVin = vin,
                    stopChargingRequest = stopChargingRequest,
                )
            }
        }

        override suspend fun fetchChargeSession(
            chargingId: String,
            make: String,
            channel: String,
            correlationid: String,
            locale: String,
            vin: String,
        ): Resource<ChargeSessionData?> {
            return makeApiCall {
                publicChargingApi.fetchChargeSession(
                    id = chargingId,
                    xMake = make,
                    xChannel = channel,
                    xCorrelationid = correlationid,
                    xLocale = locale,
                    xVin = vin,
                )
            }
        }
    }
