package com.toyota.oneapp.features.publiccharging.presentation

import androidx.compose.foundation.Image
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Chip
import androidx.compose.material.ChipDefaults
import androidx.compose.material.ExperimentalMaterialApi
import androidx.compose.material.Icon
import androidx.compose.material.Surface
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import com.toyota.oneapp.R
import com.toyota.oneapp.features.core.composable.OABody4TextView
import com.toyota.oneapp.features.core.composable.OACallOut2TextView
import com.toyota.oneapp.features.core.composable.OACaption1TextView
import com.toyota.oneapp.features.core.composable.testTagID
import com.toyota.oneapp.features.core.theme.AppTheme
import com.toyota.oneapp.features.core.util.AccessibilityId
import com.toyota.oneapp.features.publiccharging.application.getPlugValue
import com.toyota.oneapp.features.publiccharging.domain.model.ChargeStationInfo
import com.toyota.oneapp.util.ToyotaConstants

@OptIn(ExperimentalMaterialApi::class)
@Composable
fun PlugTypeChip(
    plugType: String,
    plugValue: String,
) {
    Chip(
        onClick = {},
        colors =
            ChipDefaults.chipColors(
                backgroundColor = AppTheme.colors.success02,
            ),
    ) {
        Row {
            OACallOut2TextView(
                text = plugValue,
                color = AppTheme.colors.button02a,
                modifier = Modifier.padding(bottom = 4.dp),
            )
            Spacer(modifier = Modifier.width(4.dp))
            OACaption1TextView(
                text = plugType,
                color = AppTheme.colors.button02a,
                modifier = Modifier.padding(top = 2.dp),
            )
        }
    }
}

@OptIn(ExperimentalMaterialApi::class)
@Composable
fun StationFilterChip(
    filterMenuData: FilterMenuData,
    onClick: () -> Unit,
) {
    Spacer(modifier = Modifier.width(10.dp))
    val titlePair = getTitle(filterMenuData)

    Chip(
        onClick = {},
        colors =
            ChipDefaults.chipColors(
                backgroundColor =
                    if (titlePair.second == 0) {
                        AppTheme.colors.button05b
                    } else {
                        AppTheme.colors.tertiary00
                    },
            ),
    ) {
        Row(
            modifier =
                Modifier.clickable {
                    onClick()
                },
        ) {
            OACallOut2TextView(
                text = titlePair.first,
                color =
                    if (titlePair.second == 0) {
                        AppTheme.colors.tertiary05
                    } else {
                        AppTheme.colors.tertiary15
                    },
                modifier = Modifier.padding(start = 4.dp, bottom = 12.dp, end = 4.dp, top = 8.dp),
            )
            Spacer(modifier = Modifier.width(4.dp))
            if (filterMenuData.filterType == stringResource(R.string.partners) ||
                filterMenuData.filterType ==
                stringResource(
                    R.string.plug_types,
                )
            ) {
                if (titlePair.second == 0) {
                    Icon(
                        painter = painterResource(id = R.drawable.ic_downward_arrow),
                        contentDescription = stringResource(id = R.string.right_chevron),
                        modifier =
                            Modifier
                                .padding(end = 4.dp)
                                .align(Alignment.CenterVertically),
                    )
                } else {
                    CircleTextView(titlePair.second.toString())
                }
            }
        }
    }
}

@Composable
fun getTitle(filterMenuData: FilterMenuData): Pair<String, Int> {
    var count = 0
    var selectedName = ToyotaConstants.EMPTY_STRING
    filterMenuData.filterData.forEach {
        if (it.isSelected) {
            selectedName = stringResource(it.filterName)
            count++
        }
    }
    if (count != 1) {
        selectedName = filterMenuData.filterType
    }

    return Pair(selectedName, count)
}

@OptIn(ExperimentalMaterialApi::class)
@Composable
fun CircleImageView(
    icon: Int,
    modifier: Modifier = Modifier,
    onClick: () -> Unit,
) {
    Surface(
        shape = CircleShape,
        color = AppTheme.colors.button02b,
        modifier =
            modifier
                .size(72.dp)
                .padding(12.dp),
        onClick = onClick,
    ) {
        Image(
            modifier =
                Modifier
                    .padding(12.dp)
                    .testTagID(AccessibilityId.PLUG_TYPE_CIRCLE_IMAGE),
            painter = painterResource(id = icon),
            contentDescription = null,
        )
    }
}

@Composable
fun CircleTextView(text: String) {
    Surface(
        shape = CircleShape,
        color = AppTheme.colors.button02b,
        modifier =
            Modifier
                .size(72.dp)
                .padding(12.dp),
    ) {
        OACallOut2TextView(
            modifier =
                Modifier
                    .padding(12.dp)
                    .testTagID(AccessibilityId.PLUG_TYPE_CIRCLE_TEXT),
            text = text,
            color = AppTheme.colors.tertiary00,
        )
    }
}

@Composable
fun AvailablePlugs(data: ChargeStationInfo?) {
    Spacer(modifier = Modifier.height(16.dp))
    Surface(
        shape = RoundedCornerShape(10.dp),
        color = AppTheme.colors.tile02,
        modifier = Modifier.padding(horizontal = 12.dp),
    ) {
        Row(
            modifier = Modifier.padding(horizontal = 16.dp),
        ) {
            OABody4TextView(
                text = stringResource(R.string.available_plugs),
                color = AppTheme.colors.tertiary03,
                modifier = Modifier.padding(start = 4.dp, bottom = 8.dp, end = 4.dp, top = 12.dp),
            )
            Spacer(modifier = Modifier.weight(0.5f))
            val plugAvailability = data?.evConnectorSum?.getPlugValue()
            if (plugAvailability?.isNotEmpty() == true) {
                LazyRow {
                    items(plugAvailability) { item ->
                        PlugTypeChip(
                            item[0],
                            item[1],
                        )
                    }
                }
            }
        }
    }
}
