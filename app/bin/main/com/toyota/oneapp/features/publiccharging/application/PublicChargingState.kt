package com.toyota.oneapp.features.publiccharging.application

import com.google.android.gms.maps.model.LatLng
import com.toyota.oneapp.features.publiccharging.domain.model.ChargeStationInfo

sealed class PublicChargingState {
    data object Init : PublicChargingState()

    data object Loading : PublicChargingState()

    class Success(
        val data: List<ChargeStationInfo>,
        val selectedLatLng: LatLng?,
    ) : PublicChargingState()

    data object EmptyStations : PublicChargingState()
}
