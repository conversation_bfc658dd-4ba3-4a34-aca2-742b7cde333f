package com.toyota.oneapp.features.publiccharging.application

import com.toyota.oneapp.features.publiccharging.domain.model.ChargeSessionData
import com.toyota.oneapp.features.publiccharging.domain.model.ChargeStationInfo
import com.toyota.oneapp.features.publiccharging.domain.model.StartChargingRequest
import com.toyota.oneapp.features.publiccharging.domain.model.StopChargingData
import com.toyota.oneapp.features.publiccharging.domain.model.StopChargingRequest
import com.toyota.oneapp.model.vehicle.VehicleInfo
import kotlinx.coroutines.flow.Flow

interface PublicChargingUseCase {
    fun fetchNearByStations(
        lat: Double?,
        long: Double?,
        vehicleInfo: VehicleInfo,
    ): Flow<List<ChargeStationInfo>>

    fun startCharging(
        vehicleInfo: VehicleInfo,
        country: String,
        channel: String,
        locale: String,
        startChargingRequest: StartChargingRequest,
    ): Flow<String?>

    fun stopCharging(
        vehicleInfo: VehicleInfo,
        channel: String,
        locale: String,
        stopChargingRequest: StopChargingRequest,
    ): Flow<StopChargingData?>

    fun fetchChargeSession(
        chargingId: String,
        vehicleInfo: VehicleInfo,
        channel: String,
        locale: String,
    ): Flow<ChargeSessionData?>
}
